const express = require('express');
const router = express.Router();
const {
  register,
  login,
  getMe,
  updateProfile,
  changePassword
} = require('../controllers/authController');
const { protect } = require('../middleware/auth');
const { handleValidationErrors } = require('../middleware/validation');
const {
  validateRegister,
  validateLogin,
  validateProfileUpdate,
  validatePasswordChange
} = require('../middleware/validators');

// @desc    Test registration data
// @route   POST /api/auth/test-register
// @access  Public
router.post('/test-register', (req, res) => {
  console.log('Test registration body:', req.body);
  res.json({ success: true, body: req.body });
});

// @desc    Register user
// @route   POST /api/auth/register
// @access  Public
router.post('/register', validateRegister, handleValidationErrors, register);

// @desc    Login user
// @route   POST /api/auth/login
// @access  Public
router.post('/login', validateLogin, handleValidationErrors, login);

// @desc    Get current user
// @route   GET /api/auth/me
// @access  Private
router.get('/me', protect, getMe);

// @desc    Update user profile
// @route   PUT /api/auth/profile
// @access  Private
router.put('/profile', protect, validateProfileUpdate, handleValidationErrors, updateProfile);

// @desc    Change password
// @route   PUT /api/auth/password
// @access  Private
router.put('/password', protect, validatePasswordChange, handleValidationErrors, changePassword);

module.exports = router;
