{"version": 3, "file": "index.umd.js", "sources": ["../src/utils/isCheckBoxInput.ts", "../src/utils/isDateObject.ts", "../src/utils/isNullOrUndefined.ts", "../src/utils/isObject.ts", "../src/logic/getEventValue.ts", "../src/logic/isNameInFieldArray.ts", "../src/logic/getNodeParentName.ts", "../src/utils/isWeb.ts", "../src/utils/cloneObject.ts", "../src/utils/isPlainObject.ts", "../src/utils/isKey.ts", "../src/utils/isUndefined.ts", "../src/utils/compact.ts", "../src/utils/stringToPath.ts", "../src/utils/get.ts", "../src/utils/isBoolean.ts", "../src/utils/set.ts", "../src/constants.ts", "../src/useFormContext.tsx", "../src/logic/getProxyFormState.ts", "../src/useIsomorphicLayoutEffect.ts", "../src/useFormState.ts", "../src/utils/isString.ts", "../src/logic/generateWatchOutput.ts", "../src/useWatch.ts", "../src/useController.ts", "../src/controller.tsx", "../src/utils/flatten.ts", "../src/form.tsx", "../src/logic/appendErrors.ts", "../src/utils/convertToArrayPayload.ts", "../src/utils/createSubject.ts", "../src/utils/isPrimitive.ts", "../src/utils/deepEqual.ts", "../src/utils/isEmptyObject.ts", "../src/utils/isFileInput.ts", "../src/utils/isFunction.ts", "../src/utils/isHTMLElement.ts", "../src/utils/isMultipleSelect.ts", "../src/utils/isRadioInput.ts", "../src/utils/live.ts", "../src/utils/unset.ts", "../src/utils/objectHasFunction.ts", "../src/logic/getDirtyFields.ts", "../src/logic/getCheckboxValue.ts", "../src/logic/getFieldValueAs.ts", "../src/logic/getRadioValue.ts", "../src/logic/getFieldValue.ts", "../src/logic/getResolverOptions.ts", "../src/utils/isRegex.ts", "../src/logic/getRuleValue.ts", "../src/logic/getValidationModes.ts", "../src/logic/hasPromiseValidation.ts", "../src/logic/isWatched.ts", "../src/logic/iterateFieldsByAction.ts", "../src/logic/schemaErrorLookup.ts", "../src/logic/shouldRenderFormState.ts", "../src/logic/updateFieldArrayRootError.ts", "../src/utils/isMessage.ts", "../src/logic/getValidateError.ts", "../src/logic/getValueAndMessage.ts", "../src/logic/validateField.ts", "../src/logic/createFormControl.ts", "../src/logic/hasValidation.ts", "../src/logic/skipValidation.ts", "../src/logic/shouldSubscribeByName.ts", "../src/utils/isRadioOrCheckbox.ts", "../src/logic/unsetEmptyArray.ts", "../src/logic/generateId.ts", "../src/logic/getFocusFieldName.ts", "../src/utils/append.ts", "../src/utils/fillEmptyArray.ts", "../src/utils/insert.ts", "../src/utils/move.ts", "../src/utils/prepend.ts", "../src/utils/remove.ts", "../src/utils/swap.ts", "../src/utils/update.ts", "../src/useFieldArray.ts", "../src/useForm.ts"], "sourcesContent": ["import type { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLInputElement =>\n  element.type === 'checkbox';\n", "export default (value: unknown): value is Date => value instanceof Date;\n", "export default (value: unknown): value is null | undefined => value == null;\n", "import isDateObject from './isDateObject';\nimport isNullOrUndefined from './isNullOrUndefined';\n\nexport const isObjectType = (value: unknown): value is object =>\n  typeof value === 'object';\n\nexport default <T extends object>(value: unknown): value is T =>\n  !isNullOrUndefined(value) &&\n  !Array.isArray(value) &&\n  isObjectType(value) &&\n  !isDateObject(value);\n", "import isCheckBoxInput from '../utils/isCheckBoxInput';\nimport isObject from '../utils/isObject';\n\ntype Event = { target: any };\n\nexport default (event: unknown) =>\n  isObject(event) && (event as Event).target\n    ? isCheckBoxInput((event as Event).target)\n      ? (event as Event).target.checked\n      : (event as Event).target.value\n    : event;\n", "import type { InternalFieldName } from '../types';\n\nimport getNodeParentName from './getNodeParentName';\n\nexport default (names: Set<InternalFieldName>, name: InternalFieldName) =>\n  names.has(getNodeParentName(name));\n", "export default (name: string) =>\n  name.substring(0, name.search(/\\.\\d+(\\.|$)/)) || name;\n", "export default typeof window !== 'undefined' &&\n  typeof window.HTMLElement !== 'undefined' &&\n  typeof document !== 'undefined';\n", "import isObject from './isObject';\nimport isPlainObject from './isPlainObject';\nimport isWeb from './isWeb';\n\nexport default function cloneObject<T>(data: T): T {\n  let copy: any;\n  const isArray = Array.isArray(data);\n  const isFileListInstance =\n    typeof FileList !== 'undefined' ? data instanceof FileList : false;\n\n  if (data instanceof Date) {\n    copy = new Date(data);\n  } else if (\n    !(isWeb && (data instanceof Blob || isFileListInstance)) &&\n    (isArray || isObject(data))\n  ) {\n    copy = isArray ? [] : {};\n\n    if (!isArray && !isPlainObject(data)) {\n      copy = data;\n    } else {\n      for (const key in data) {\n        if (data.hasOwnProperty(key)) {\n          copy[key] = cloneObject(data[key]);\n        }\n      }\n    }\n  } else {\n    return data;\n  }\n\n  return copy;\n}\n", "import isObject from './isObject';\n\nexport default (tempObject: object) => {\n  const prototypeCopy =\n    tempObject.constructor && tempObject.constructor.prototype;\n\n  return (\n    isObject(prototypeCopy) && prototypeCopy.hasOwnProperty('isPrototypeOf')\n  );\n};\n", "export default (value: string) => /^\\w*$/.test(value);\n", "export default (val: unknown): val is undefined => val === undefined;\n", "export default <TValue>(value: TValue[]) =>\n  Array.isArray(value) ? value.filter(Boolean) : [];\n", "import compact from './compact';\n\nexport default (input: string): string[] =>\n  compact(input.replace(/[\"|']|\\]/g, '').split(/\\.|\\[/));\n", "import isKey from './isKey';\nimport isNullOrUndefined from './isNullOrUndefined';\nimport isObject from './isObject';\nimport isUndefined from './isUndefined';\nimport stringToPath from './stringToPath';\n\nexport default <T>(\n  object: T,\n  path?: string | null,\n  defaultValue?: unknown,\n): any => {\n  if (!path || !isObject(object)) {\n    return defaultValue;\n  }\n\n  const result = (isKey(path) ? [path] : stringToPath(path)).reduce(\n    (result, key) =>\n      isNullOrUndefined(result) ? result : result[key as keyof {}],\n    object,\n  );\n\n  return isUndefined(result) || result === object\n    ? isUndefined(object[path as keyof T])\n      ? defaultValue\n      : object[path as keyof T]\n    : result;\n};\n", "export default (value: unknown): value is boolean => typeof value === 'boolean';\n", "import type { FieldPath, FieldValues } from '../types';\n\nimport isKey from './isKey';\nimport isObject from './isObject';\nimport stringToPath from './stringToPath';\n\nexport default (\n  object: FieldValues,\n  path: FieldPath<FieldValues>,\n  value?: unknown,\n) => {\n  let index = -1;\n  const tempPath = isKey(path) ? [path] : stringToPath(path);\n  const length = tempPath.length;\n  const lastIndex = length - 1;\n\n  while (++index < length) {\n    const key = tempPath[index];\n    let newValue = value;\n\n    if (index !== lastIndex) {\n      const objValue = object[key];\n      newValue =\n        isObject(objValue) || Array.isArray(objValue)\n          ? objValue\n          : !isNaN(+tempPath[index + 1])\n            ? []\n            : {};\n    }\n\n    if (key === '__proto__' || key === 'constructor' || key === 'prototype') {\n      return;\n    }\n\n    object[key] = newValue;\n    object = object[key];\n  }\n};\n", "export const EVENTS = {\n  BLUR: 'blur',\n  FOCUS_OUT: 'focusout',\n  CHANGE: 'change',\n} as const;\n\nexport const VALIDATION_MODE = {\n  onBlur: 'onBlur',\n  onChange: 'onChange',\n  onSubmit: 'onSubmit',\n  onTouched: 'onTouched',\n  all: 'all',\n} as const;\n\nexport const INPUT_VALIDATION_RULES = {\n  max: 'max',\n  min: 'min',\n  maxLength: 'maxLength',\n  minLength: 'minLength',\n  pattern: 'pattern',\n  required: 'required',\n  validate: 'validate',\n} as const;\n", "import React from 'react';\n\nimport type { FieldValues, FormProviderProps, UseFormReturn } from './types';\n\nconst HookFormContext = React.createContext<UseFormReturn | null>(null);\nHookFormContext.displayName = 'HookFormContext';\n\n/**\n * This custom hook allows you to access the form context. useFormContext is intended to be used in deeply nested structures, where it would become inconvenient to pass the context as a prop. To be used with {@link FormProvider}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @returns return all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nexport const useFormContext = <\n  TFieldValues extends FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(): UseFormReturn<TFieldValues, TContext, TTransformedValues> =>\n  React.useContext(HookFormContext) as UseFormReturn<\n    TFieldValues,\n    TContext,\n    TTransformedValues\n  >;\n\n/**\n * A provider component that propagates the `useForm` methods to all children components via [React Context](https://reactjs.org/docs/context.html) API. To be used with {@link useFormContext}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @param props - all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nexport const FormProvider = <\n  TFieldValues extends FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(\n  props: FormProviderProps<TFieldValues, TContext, TTransformedValues>,\n) => {\n  const { children, ...data } = props;\n  return (\n    <HookFormContext.Provider value={data as unknown as UseFormReturn}>\n      {children}\n    </HookFormContext.Provider>\n  );\n};\n", "import { VALIDATION_MODE } from '../constants';\nimport type { Control, FieldValues, FormState, ReadFormState } from '../types';\n\nexport default <\n  TFieldValues extends FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(\n  formState: FormState<TFieldValues>,\n  control: Control<TFieldValues, TContext, TTransformedValues>,\n  localProxyFormState?: ReadFormState,\n  isRoot = true,\n) => {\n  const result = {\n    defaultValues: control._defaultValues,\n  } as typeof formState;\n\n  for (const key in formState) {\n    Object.defineProperty(result, key, {\n      get: () => {\n        const _key = key as keyof FormState<TFieldValues> & keyof ReadFormState;\n\n        if (control._proxyFormState[_key] !== VALIDATION_MODE.all) {\n          control._proxyFormState[_key] = !isRoot || VALIDATION_MODE.all;\n        }\n\n        localProxyFormState && (localProxyFormState[_key] = true);\n        return formState[_key];\n      },\n    });\n  }\n\n  return result;\n};\n", "import * as React from 'react';\n\nexport const useIsomorphicLayoutEffect =\n  typeof window !== 'undefined' ? React.useLayoutEffect : React.useEffect;\n", "import React from 'react';\n\nimport getProxyFormState from './logic/getProxyFormState';\nimport type {\n  FieldValues,\n  UseFormStateProps,\n  UseFormStateReturn,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect';\n\n/**\n * This custom hook allows you to subscribe to each form state, and isolate the re-render at the custom hook level. It has its scope in terms of form state subscription, so it would not affect other useFormState and useForm. Using this hook can reduce the re-render impact on large and complex form application.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformstate) • [Demo](https://codesandbox.io/s/useformstate-75xly)\n *\n * @param props - include options on specify fields to subscribe. {@link UseFormStateReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, control } = useForm({\n *     defaultValues: {\n *     firstName: \"firstName\"\n *   }});\n *   const { dirtyFields } = useFormState({\n *     control\n *   });\n *   const onSubmit = (data) => console.log(data);\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input {...register(\"firstName\")} placeholder=\"First Name\" />\n *       {dirtyFields.firstName && <p>Field is dirty.</p>}\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nexport function useFormState<\n  TFieldValues extends FieldValues = FieldValues,\n  TTransformedValues = TFieldValues,\n>(\n  props?: UseFormStateProps<TFieldValues, TTransformedValues>,\n): UseFormStateReturn<TFieldValues> {\n  const methods = useFormContext<TFieldValues, any, TTransformedValues>();\n  const { control = methods.control, disabled, name, exact } = props || {};\n  const [formState, updateFormState] = React.useState(control._formState);\n  const _localProxyFormState = React.useRef({\n    isDirty: false,\n    isLoading: false,\n    dirtyFields: false,\n    touchedFields: false,\n    validatingFields: false,\n    isValidating: false,\n    isValid: false,\n    errors: false,\n  });\n\n  useIsomorphicLayoutEffect(\n    () =>\n      control._subscribe({\n        name,\n        formState: _localProxyFormState.current,\n        exact,\n        callback: (formState) => {\n          !disabled &&\n            updateFormState({\n              ...control._formState,\n              ...formState,\n            });\n        },\n      }),\n    [name, disabled, exact],\n  );\n\n  React.useEffect(() => {\n    _localProxyFormState.current.isValid && control._setValid(true);\n  }, [control]);\n\n  return React.useMemo(\n    () =>\n      getProxyFormState(\n        formState,\n        control,\n        _localProxyFormState.current,\n        false,\n      ),\n    [formState, control],\n  );\n}\n", "export default (value: unknown): value is string => typeof value === 'string';\n", "import type { DeepPartial, FieldValues, Names } from '../types';\nimport get from '../utils/get';\nimport isString from '../utils/isString';\n\nexport default <T>(\n  names: string | string[] | undefined,\n  _names: Names,\n  formValues?: FieldValues,\n  isGlobal?: boolean,\n  defaultValue?: DeepPartial<T> | unknown,\n) => {\n  if (isString(names)) {\n    isGlobal && _names.watch.add(names);\n    return get(formValues, names, defaultValue);\n  }\n\n  if (Array.isArray(names)) {\n    return names.map(\n      (fieldName) => (\n        isGlobal && _names.watch.add(fieldName),\n        get(formValues, fieldName)\n      ),\n    );\n  }\n\n  isGlobal && (_names.watchAll = true);\n\n  return formValues;\n};\n", "import React from 'react';\n\nimport generateWatchOutput from './logic/generateWatchOutput';\nimport type {\n  Control,\n  DeepPartialSkipArrayKey,\n  FieldPath,\n  FieldPathValue,\n  FieldPathValues,\n  FieldValues,\n  InternalFieldName,\n  UseWatchProps,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect';\n\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   name: \"fieldA\",\n *   defaultValue: \"default value\",\n *   exact: false,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n  TTransformedValues = TFieldValues,\n>(props: {\n  name: TFieldName;\n  defaultValue?: FieldPathValue<TFieldValues, TFieldName>;\n  control?: Control<TFieldValues, any, TTransformedValues>;\n  disabled?: boolean;\n  exact?: boolean;\n}): FieldPathValue<TFieldValues, TFieldName>;\n/**\n * Subscribe to the entire form values change and re-render at the hook level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   defaultValue: {\n *     name: \"data\"\n *   },\n *   exact: false,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n  TTransformedValues = TFieldValues,\n>(props: {\n  defaultValue?: DeepPartialSkipArrayKey<TFieldValues>;\n  control?: Control<TFieldValues, any, TTransformedValues>;\n  disabled?: boolean;\n  exact?: boolean;\n}): DeepPartialSkipArrayKey<TFieldValues>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   name: [\"fieldA\", \"fieldB\"],\n *   defaultValue: {\n *     fieldA: \"data\",\n *     fieldB: \"data\"\n *   },\n *   exact: false,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldNames extends\n    readonly FieldPath<TFieldValues>[] = readonly FieldPath<TFieldValues>[],\n  TTransformedValues = TFieldValues,\n>(props: {\n  name: readonly [...TFieldNames];\n  defaultValue?: DeepPartialSkipArrayKey<TFieldValues>;\n  control?: Control<TFieldValues, any, TTransformedValues>;\n  disabled?: boolean;\n  exact?: boolean;\n}): FieldPathValues<TFieldValues, TFieldNames>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * // can skip passing down the control into useWatch if the form is wrapped with the FormProvider\n * const values = useWatch()\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n>(): DeepPartialSkipArrayKey<TFieldValues>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   name: \"fieldName\"\n *   control,\n * })\n * ```\n */\nexport function useWatch<TFieldValues extends FieldValues>(\n  props?: UseWatchProps<TFieldValues>,\n) {\n  const methods = useFormContext<TFieldValues>();\n  const {\n    control = methods.control,\n    name,\n    defaultValue,\n    disabled,\n    exact,\n  } = props || {};\n  const _defaultValue = React.useRef(defaultValue);\n  const [value, updateValue] = React.useState(\n    control._getWatch(\n      name as InternalFieldName,\n      _defaultValue.current as DeepPartialSkipArrayKey<TFieldValues>,\n    ),\n  );\n\n  useIsomorphicLayoutEffect(\n    () =>\n      control._subscribe({\n        name,\n        formState: {\n          values: true,\n        },\n        exact,\n        callback: (formState) =>\n          !disabled &&\n          updateValue(\n            generateWatchOutput(\n              name as InternalFieldName | InternalFieldName[],\n              control._names,\n              formState.values || control._formValues,\n              false,\n              _defaultValue.current,\n            ),\n          ),\n      }),\n    [name, control, disabled, exact],\n  );\n\n  React.useEffect(() => control._removeUnmounted());\n\n  return value;\n}\n", "import React from 'react';\n\nimport getEventValue from './logic/getEventValue';\nimport isNameInFieldArray from './logic/isNameInFieldArray';\nimport cloneObject from './utils/cloneObject';\nimport get from './utils/get';\nimport isBoolean from './utils/isBoolean';\nimport isUndefined from './utils/isUndefined';\nimport set from './utils/set';\nimport { EVENTS } from './constants';\nimport type {\n  ControllerFieldState,\n  Field,\n  FieldPath,\n  FieldPathValue,\n  FieldValues,\n  InternalFieldName,\n  UseControllerProps,\n  UseControllerReturn,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useFormState } from './useFormState';\nimport { useWatch } from './useWatch';\n\n/**\n * Custom hook to work with controlled component, this function provide you with both form and field level state. Re-render is isolated at the hook level.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller) • [Demo](https://codesandbox.io/s/usecontroller-0o8px)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns field properties, field and form state. {@link UseControllerReturn}\n *\n * @example\n * ```tsx\n * function Input(props) {\n *   const { field, fieldState, formState } = useController(props);\n *   return (\n *     <div>\n *       <input {...field} placeholder={props.name} />\n *       <p>{fieldState.isTouched && \"Touched\"}</p>\n *       <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *     </div>\n *   );\n * }\n * ```\n */\nexport function useController<\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n  TTransformedValues = TFieldValues,\n>(\n  props: UseControllerProps<TFieldValues, TName, TTransformedValues>,\n): UseControllerReturn<TFieldValues, TName> {\n  const methods = useFormContext<TFieldValues, any, TTransformedValues>();\n  const { name, disabled, control = methods.control, shouldUnregister } = props;\n  const isArrayField = isNameInFieldArray(control._names.array, name);\n  const value = useWatch({\n    control,\n    name,\n    defaultValue: get(\n      control._formValues,\n      name,\n      get(control._defaultValues, name, props.defaultValue),\n    ),\n    exact: true,\n  }) as FieldPathValue<TFieldValues, TName>;\n  const formState = useFormState({\n    control,\n    name,\n    exact: true,\n  });\n\n  const _props = React.useRef(props);\n  const _registerProps = React.useRef(\n    control.register(name, {\n      ...props.rules,\n      value,\n      ...(isBoolean(props.disabled) ? { disabled: props.disabled } : {}),\n    }),\n  );\n\n  const fieldState = React.useMemo(\n    () =>\n      Object.defineProperties(\n        {},\n        {\n          invalid: {\n            enumerable: true,\n            get: () => !!get(formState.errors, name),\n          },\n          isDirty: {\n            enumerable: true,\n            get: () => !!get(formState.dirtyFields, name),\n          },\n          isTouched: {\n            enumerable: true,\n            get: () => !!get(formState.touchedFields, name),\n          },\n          isValidating: {\n            enumerable: true,\n            get: () => !!get(formState.validatingFields, name),\n          },\n          error: {\n            enumerable: true,\n            get: () => get(formState.errors, name),\n          },\n        },\n      ) as ControllerFieldState,\n    [formState, name],\n  );\n\n  const onChange = React.useCallback(\n    (event: any) =>\n      _registerProps.current.onChange({\n        target: {\n          value: getEventValue(event),\n          name: name as InternalFieldName,\n        },\n        type: EVENTS.CHANGE,\n      }),\n    [name],\n  );\n\n  const onBlur = React.useCallback(\n    () =>\n      _registerProps.current.onBlur({\n        target: {\n          value: get(control._formValues, name),\n          name: name as InternalFieldName,\n        },\n        type: EVENTS.BLUR,\n      }),\n    [name, control._formValues],\n  );\n\n  const ref = React.useCallback(\n    (elm: any) => {\n      const field = get(control._fields, name);\n\n      if (field && elm) {\n        field._f.ref = {\n          focus: () => elm.focus && elm.focus(),\n          select: () => elm.select && elm.select(),\n          setCustomValidity: (message: string) =>\n            elm.setCustomValidity(message),\n          reportValidity: () => elm.reportValidity(),\n        };\n      }\n    },\n    [control._fields, name],\n  );\n\n  const field = React.useMemo(\n    () => ({\n      name,\n      value,\n      ...(isBoolean(disabled) || formState.disabled\n        ? { disabled: formState.disabled || disabled }\n        : {}),\n      onChange,\n      onBlur,\n      ref,\n    }),\n    [name, disabled, formState.disabled, onChange, onBlur, ref, value],\n  );\n\n  React.useEffect(() => {\n    const _shouldUnregisterField =\n      control._options.shouldUnregister || shouldUnregister;\n\n    control.register(name, {\n      ..._props.current.rules,\n      ...(isBoolean(_props.current.disabled)\n        ? { disabled: _props.current.disabled }\n        : {}),\n    });\n\n    const updateMounted = (name: InternalFieldName, value: boolean) => {\n      const field: Field = get(control._fields, name);\n\n      if (field && field._f) {\n        field._f.mount = value;\n      }\n    };\n\n    updateMounted(name, true);\n\n    if (_shouldUnregisterField) {\n      const value = cloneObject(get(control._options.defaultValues, name));\n      set(control._defaultValues, name, value);\n      if (isUndefined(get(control._formValues, name))) {\n        set(control._formValues, name, value);\n      }\n    }\n\n    !isArrayField && control.register(name);\n\n    return () => {\n      (\n        isArrayField\n          ? _shouldUnregisterField && !control._state.action\n          : _shouldUnregisterField\n      )\n        ? control.unregister(name)\n        : updateMounted(name, false);\n    };\n  }, [name, control, isArrayField, shouldUnregister]);\n\n  React.useEffect(() => {\n    control._setDisabledField({\n      disabled,\n      name,\n    });\n  }, [disabled, name, control]);\n\n  return React.useMemo(\n    () => ({\n      field,\n      formState,\n      fieldState,\n    }),\n    [field, formState, fieldState],\n  );\n}\n", "import type { ControllerProps, FieldPath, FieldValues } from './types';\nimport { useController } from './useController';\n\n/**\n * Component based on `useController` hook to work with controlled component.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller/controller) • [Demo](https://codesandbox.io/s/react-hook-form-v6-controller-ts-jwyzw) • [Video](https://www.youtube.com/watch?v=N2UNk_UCVyA)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns provide field handler functions, field and form state.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control } = useForm<FormValues>({\n *     defaultValues: {\n *       test: \"\"\n *     }\n *   });\n *\n *   return (\n *     <form>\n *       <Controller\n *         control={control}\n *         name=\"test\"\n *         render={({ field: { onChange, onBlur, value, ref }, formState, fieldState }) => (\n *           <>\n *             <input\n *               onChange={onChange} // send value to hook form\n *               onBlur={onBlur} // notify when input is touched\n *               value={value} // return updated value\n *               ref={ref} // set ref for focus management\n *             />\n *             <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *             <p>{fieldState.isTouched ? \"touched\" : \"\"}</p>\n *           </>\n *         )}\n *       />\n *     </form>\n *   );\n * }\n * ```\n */\nconst Controller = <\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n  TTransformedValues = TFieldValues,\n>(\n  props: ControllerProps<TFieldValues, TName, TTransformedValues>,\n) =>\n  props.render(useController<TFieldValues, TName, TTransformedValues>(props));\n\nexport { Controller };\n", "import type { FieldValues } from '../types';\n\nimport { isObjectType } from './isObject';\n\nexport const flatten = (obj: FieldValues) => {\n  const output: FieldValues = {};\n\n  for (const key of Object.keys(obj)) {\n    if (isObjectType(obj[key]) && obj[key] !== null) {\n      const nested = flatten(obj[key]);\n\n      for (const nestedKey of Object.keys(nested)) {\n        output[`${key}.${nestedKey}`] = nested[nestedKey];\n      }\n    } else {\n      output[key] = obj[key];\n    }\n  }\n\n  return output;\n};\n", "import React from 'react';\n\nimport { flatten } from './utils/flatten';\nimport type { FieldValues, FormProps } from './types';\nimport { useFormContext } from './useFormContext';\n\nconst POST_REQUEST = 'post';\n\n/**\n * Form component to manage submission.\n *\n * @param props - to setup submission detail. {@link FormProps}\n *\n * @returns form component or headless render prop.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control, formState: { errors } } = useForm();\n *\n *   return (\n *     <Form action=\"/api\" control={control}>\n *       <input {...register(\"name\")} />\n *       <p>{errors?.root?.server && 'Server error'}</p>\n *       <button>Submit</button>\n *     </Form>\n *   );\n * }\n * ```\n */\nfunction Form<\n  TFieldValues extends FieldValues,\n  TTransformedValues = TFieldValues,\n>(props: FormProps<TFieldValues, TTransformedValues>) {\n  const methods = useFormContext<TFieldValues, any, TTransformedValues>();\n  const [mounted, setMounted] = React.useState(false);\n  const {\n    control = methods.control,\n    onSubmit,\n    children,\n    action,\n    method = POST_REQUEST,\n    headers,\n    encType,\n    onError,\n    render,\n    onSuccess,\n    validateStatus,\n    ...rest\n  } = props;\n\n  const submit = async (event?: React.BaseSyntheticEvent) => {\n    let hasError = false;\n    let type = '';\n\n    await control.handleSubmit(async (data) => {\n      const formData = new FormData();\n      let formDataJson = '';\n\n      try {\n        formDataJson = JSON.stringify(data);\n      } catch {}\n\n      const flattenFormValues = flatten(control._formValues);\n\n      for (const key in flattenFormValues) {\n        formData.append(key, flattenFormValues[key]);\n      }\n\n      if (onSubmit) {\n        await onSubmit({\n          data,\n          event,\n          method,\n          formData,\n          formDataJson,\n        });\n      }\n\n      if (action) {\n        try {\n          const shouldStringifySubmissionData = [\n            headers && headers['Content-Type'],\n            encType,\n          ].some((value) => value && value.includes('json'));\n\n          const response = await fetch(String(action), {\n            method,\n            headers: {\n              ...headers,\n              ...(encType ? { 'Content-Type': encType } : {}),\n            },\n            body: shouldStringifySubmissionData ? formDataJson : formData,\n          });\n\n          if (\n            response &&\n            (validateStatus\n              ? !validateStatus(response.status)\n              : response.status < 200 || response.status >= 300)\n          ) {\n            hasError = true;\n            onError && onError({ response });\n            type = String(response.status);\n          } else {\n            onSuccess && onSuccess({ response });\n          }\n        } catch (error: unknown) {\n          hasError = true;\n          onError && onError({ error });\n        }\n      }\n    })(event);\n\n    if (hasError && props.control) {\n      props.control._subjects.state.next({\n        isSubmitSuccessful: false,\n      });\n      props.control.setError('root.server', {\n        type,\n      });\n    }\n  };\n\n  React.useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  return render ? (\n    <>\n      {render({\n        submit,\n      })}\n    </>\n  ) : (\n    <form\n      noValidate={mounted}\n      action={action}\n      method={method}\n      encType={encType}\n      onSubmit={submit}\n      {...rest}\n    >\n      {children}\n    </form>\n  );\n}\n\nexport { Form };\n", "import type {\n  InternalFieldErrors,\n  InternalFieldName,\n  ValidateResult,\n} from '../types';\n\nexport default (\n  name: InternalFieldName,\n  validateAllFieldCriteria: boolean,\n  errors: InternalFieldErrors,\n  type: string,\n  message: ValidateResult,\n) =>\n  validateAllFieldCriteria\n    ? {\n        ...errors[name],\n        types: {\n          ...(errors[name] && errors[name]!.types ? errors[name]!.types : {}),\n          [type]: message || true,\n        },\n      }\n    : {};\n", "export default <T>(value: T) => (Array.isArray(value) ? value : [value]);\n", "import type { Noop } from '../types';\n\nexport type Observer<T> = {\n  next: (value: T) => void;\n};\n\nexport type Subscription = {\n  unsubscribe: Noop;\n};\n\nexport type Subject<T> = {\n  readonly observers: Observer<T>[];\n  subscribe: (value: Observer<T>) => Subscription;\n  unsubscribe: Noop;\n} & Observer<T>;\n\nexport default <T>(): Subject<T> => {\n  let _observers: Observer<T>[] = [];\n\n  const next = (value: T) => {\n    for (const observer of _observers) {\n      observer.next && observer.next(value);\n    }\n  };\n\n  const subscribe = (observer: Observer<T>): Subscription => {\n    _observers.push(observer);\n    return {\n      unsubscribe: () => {\n        _observers = _observers.filter((o) => o !== observer);\n      },\n    };\n  };\n\n  const unsubscribe = () => {\n    _observers = [];\n  };\n\n  return {\n    get observers() {\n      return _observers;\n    },\n    next,\n    subscribe,\n    unsubscribe,\n  };\n};\n", "import type { Primitive } from '../types';\n\nimport isNullOrUndefined from './isNullOrUndefined';\nimport { isObjectType } from './isObject';\n\nexport default (value: unknown): value is Primitive =>\n  isNullOrUndefined(value) || !isObjectType(value);\n", "import isObject from '../utils/isObject';\n\nimport isDateObject from './isDateObject';\nimport isPrimitive from './isPrimitive';\n\nexport default function deepEqual(\n  object1: any,\n  object2: any,\n  _internal_visited = new WeakSet(),\n) {\n  if (isPrimitive(object1) || isPrimitive(object2)) {\n    return object1 === object2;\n  }\n\n  if (isDateObject(object1) && isDateObject(object2)) {\n    return object1.getTime() === object2.getTime();\n  }\n\n  const keys1 = Object.keys(object1);\n  const keys2 = Object.keys(object2);\n\n  if (keys1.length !== keys2.length) {\n    return false;\n  }\n\n  if (_internal_visited.has(object1) || _internal_visited.has(object2)) {\n    return true;\n  }\n  _internal_visited.add(object1);\n  _internal_visited.add(object2);\n\n  for (const key of keys1) {\n    const val1 = object1[key];\n\n    if (!keys2.includes(key)) {\n      return false;\n    }\n\n    if (key !== 'ref') {\n      const val2 = object2[key];\n\n      if (\n        (isDateObject(val1) && isDateObject(val2)) ||\n        (isObject(val1) && isObject(val2)) ||\n        (Array.isArray(val1) && Array.isArray(val2))\n          ? !deepEqual(val1, val2, _internal_visited)\n          : val1 !== val2\n      ) {\n        return false;\n      }\n    }\n  }\n\n  return true;\n}\n", "import type { EmptyObject } from '../types';\n\nimport isObject from './isObject';\n\nexport default (value: unknown): value is EmptyObject =>\n  isObject(value) && !Object.keys(value).length;\n", "import type { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLInputElement =>\n  element.type === 'file';\n", "export default (value: unknown): value is Function =>\n  typeof value === 'function';\n", "import isWeb from './isWeb';\n\nexport default (value: unknown): value is HTMLElement => {\n  if (!isWeb) {\n    return false;\n  }\n\n  const owner = value ? ((value as HTMLElement).ownerDocument as Document) : 0;\n  return (\n    value instanceof\n    (owner && owner.defaultView ? owner.defaultView.HTMLElement : HTMLElement)\n  );\n};\n", "import type { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLSelectElement =>\n  element.type === `select-multiple`;\n", "import type { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLInputElement =>\n  element.type === 'radio';\n", "import type { Ref } from '../types';\n\nimport isHTMLElement from './isHTMLElement';\n\nexport default (ref: Ref) => isHTMLElement(ref) && ref.isConnected;\n", "import isEmptyObject from './isEmptyObject';\nimport isKey from './isKey';\nimport isObject from './isObject';\nimport isUndefined from './isUndefined';\nimport stringToPath from './stringToPath';\n\nfunction baseGet(object: any, updatePath: (string | number)[]) {\n  const length = updatePath.slice(0, -1).length;\n  let index = 0;\n\n  while (index < length) {\n    object = isUndefined(object) ? index++ : object[updatePath[index++]];\n  }\n\n  return object;\n}\n\nfunction isEmptyArray(obj: unknown[]) {\n  for (const key in obj) {\n    if (obj.hasOwnProperty(key) && !isUndefined(obj[key])) {\n      return false;\n    }\n  }\n  return true;\n}\n\nexport default function unset(object: any, path: string | (string | number)[]) {\n  const paths = Array.isArray(path)\n    ? path\n    : isKey(path)\n      ? [path]\n      : stringToPath(path);\n\n  const childObject = paths.length === 1 ? object : baseGet(object, paths);\n\n  const index = paths.length - 1;\n  const key = paths[index];\n\n  if (childObject) {\n    delete childObject[key];\n  }\n\n  if (\n    index !== 0 &&\n    ((isObject(childObject) && isEmptyObject(childObject)) ||\n      (Array.isArray(childObject) && isEmptyArray(childObject)))\n  ) {\n    unset(object, paths.slice(0, -1));\n  }\n\n  return object;\n}\n", "import isFunction from './isFunction';\n\nexport default <T>(data: T): boolean => {\n  for (const key in data) {\n    if (isFunction(data[key])) {\n      return true;\n    }\n  }\n  return false;\n};\n", "import deepEqual from '../utils/deepEqual';\nimport isNullOrUndefined from '../utils/isNullOrUndefined';\nimport isObject from '../utils/isObject';\nimport isPrimitive from '../utils/isPrimitive';\nimport isUndefined from '../utils/isUndefined';\nimport objectHasFunction from '../utils/objectHasFunction';\n\nfunction markFieldsDirty<T>(data: T, fields: Record<string, any> = {}) {\n  const isParentNodeArray = Array.isArray(data);\n\n  if (isObject(data) || isParentNodeArray) {\n    for (const key in data) {\n      if (\n        Array.isArray(data[key]) ||\n        (isObject(data[key]) && !objectHasFunction(data[key]))\n      ) {\n        fields[key] = Array.isArray(data[key]) ? [] : {};\n        markFieldsDirty(data[key], fields[key]);\n      } else if (!isNullOrUndefined(data[key])) {\n        fields[key] = true;\n      }\n    }\n  }\n\n  return fields;\n}\n\nfunction getDirtyFieldsFromDefaultValues<T>(\n  data: T,\n  formValues: T,\n  dirtyFieldsFromValues: Record<\n    Extract<keyof T, string>,\n    ReturnType<typeof markFieldsDirty> | boolean\n  >,\n) {\n  const isParentNodeArray = Array.isArray(data);\n\n  if (isObject(data) || isParentNodeArray) {\n    for (const key in data) {\n      if (\n        Array.isArray(data[key]) ||\n        (isObject(data[key]) && !objectHasFunction(data[key]))\n      ) {\n        if (\n          isUndefined(formValues) ||\n          isPrimitive(dirtyFieldsFromValues[key])\n        ) {\n          dirtyFieldsFromValues[key] = Array.isArray(data[key])\n            ? markFieldsDirty(data[key], [])\n            : { ...markFieldsDirty(data[key]) };\n        } else {\n          getDirtyFieldsFromDefaultValues(\n            data[key],\n            isNullOrUndefined(formValues) ? {} : formValues[key],\n            dirtyFieldsFromValues[key],\n          );\n        }\n      } else {\n        dirtyFieldsFromValues[key] = !deepEqual(data[key], formValues[key]);\n      }\n    }\n  }\n\n  return dirtyFieldsFromValues;\n}\n\nexport default <T>(defaultValues: T, formValues: T) =>\n  getDirtyFieldsFromDefaultValues(\n    defaultValues,\n    formValues,\n    markFieldsDirty(formValues),\n  );\n", "import isUndefined from '../utils/isUndefined';\n\ntype CheckboxFieldResult = {\n  isValid: boolean;\n  value: string | string[] | boolean | undefined;\n};\n\nconst defaultResult: CheckboxFieldResult = {\n  value: false,\n  isValid: false,\n};\n\nconst validResult = { value: true, isValid: true };\n\nexport default (options?: HTMLInputElement[]): CheckboxFieldResult => {\n  if (Array.isArray(options)) {\n    if (options.length > 1) {\n      const values = options\n        .filter((option) => option && option.checked && !option.disabled)\n        .map((option) => option.value);\n      return { value: values, isValid: !!values.length };\n    }\n\n    return options[0].checked && !options[0].disabled\n      ? // @ts-expect-error expected to work in the browser\n        options[0].attributes && !isUndefined(options[0].attributes.value)\n        ? isUndefined(options[0].value) || options[0].value === ''\n          ? validResult\n          : { value: options[0].value, isValid: true }\n        : validResult\n      : defaultResult;\n  }\n\n  return defaultResult;\n};\n", "import type { Field, NativeFieldValue } from '../types';\nimport isString from '../utils/isString';\nimport isUndefined from '../utils/isUndefined';\n\nexport default <T extends NativeFieldValue>(\n  value: T,\n  { valueAsNumber, valueAsDate, setValueAs }: Field['_f'],\n) =>\n  isUndefined(value)\n    ? value\n    : valueAsNumber\n      ? value === ''\n        ? NaN\n        : value\n          ? +value\n          : value\n      : valueAsDate && isString(value)\n        ? new Date(value)\n        : setValueAs\n          ? setValueAs(value)\n          : value;\n", "type RadioFieldResult = {\n  isValid: boolean;\n  value: number | string | null;\n};\n\nconst defaultReturn: RadioFieldResult = {\n  isValid: false,\n  value: null,\n};\n\nexport default (options?: HTMLInputElement[]): RadioFieldResult =>\n  Array.isArray(options)\n    ? options.reduce(\n        (previous, option): RadioFieldResult =>\n          option && option.checked && !option.disabled\n            ? {\n                isValid: true,\n                value: option.value,\n              }\n            : previous,\n        defaultReturn,\n      )\n    : defaultReturn;\n", "import type { Field } from '../types';\nimport isCheckBox from '../utils/isCheckBoxInput';\nimport isFileInput from '../utils/isFileInput';\nimport isMultipleSelect from '../utils/isMultipleSelect';\nimport isRadioInput from '../utils/isRadioInput';\nimport isUndefined from '../utils/isUndefined';\n\nimport getCheckboxValue from './getCheckboxValue';\nimport getFieldValueAs from './getFieldValueAs';\nimport getRadioValue from './getRadioValue';\n\nexport default function getFieldValue(_f: Field['_f']) {\n  const ref = _f.ref;\n\n  if (isFileInput(ref)) {\n    return ref.files;\n  }\n\n  if (isRadioInput(ref)) {\n    return getRadioValue(_f.refs).value;\n  }\n\n  if (isMultipleSelect(ref)) {\n    return [...ref.selectedOptions].map(({ value }) => value);\n  }\n\n  if (isCheckBox(ref)) {\n    return getCheckboxValue(_f.refs).value;\n  }\n\n  return getFieldValueAs(isUndefined(ref.value) ? _f.ref.value : ref.value, _f);\n}\n", "import type {\n  CriteriaMode,\n  Field,\n  FieldName,\n  FieldRefs,\n  FieldValues,\n  InternalFieldName,\n} from '../types';\nimport { get } from '../utils';\nimport set from '../utils/set';\n\nexport default <TFieldValues extends FieldValues>(\n  fieldsNames: Set<InternalFieldName> | InternalFieldName[],\n  _fields: FieldRefs,\n  criteriaMode?: CriteriaMode,\n  shouldUseNativeValidation?: boolean | undefined,\n) => {\n  const fields: Record<InternalFieldName, Field['_f']> = {};\n\n  for (const name of fieldsNames) {\n    const field: Field = get(_fields, name);\n\n    field && set(fields, name, field._f);\n  }\n\n  return {\n    criteriaMode,\n    names: [...fieldsNames] as FieldName<TFieldValues>[],\n    fields,\n    shouldUseNativeValidation,\n  };\n};\n", "export default (value: unknown): value is RegExp => value instanceof RegExp;\n", "import type {\n  ValidationRule,\n  ValidationValue,\n  ValidationValueMessage,\n} from '../types';\nimport isObject from '../utils/isObject';\nimport isRegex from '../utils/isRegex';\nimport isUndefined from '../utils/isUndefined';\n\nexport default <T extends ValidationValue>(\n  rule?: ValidationRule<T> | ValidationValueMessage<T>,\n) =>\n  isUndefined(rule)\n    ? rule\n    : isRegex(rule)\n      ? rule.source\n      : isObject(rule)\n        ? isRegex(rule.value)\n          ? rule.value.source\n          : rule.value\n        : rule;\n", "import { VALIDATION_MODE } from '../constants';\nimport type { Mode, ValidationModeFlags } from '../types';\n\nexport default (mode?: Mode): ValidationModeFlags => ({\n  isOnSubmit: !mode || mode === VALIDATION_MODE.onSubmit,\n  isOnBlur: mode === VALIDATION_MODE.onBlur,\n  isOnChange: mode === VALIDATION_MODE.onChange,\n  isOnAll: mode === VALIDATION_MODE.all,\n  isOnTouch: mode === VALIDATION_MODE.onTouched,\n});\n", "import type { Field, Validate } from '../types';\nimport isFunction from '../utils/isFunction';\nimport isObject from '../utils/isObject';\n\nconst ASYNC_FUNCTION = 'AsyncFunction';\n\nexport default (fieldReference: Field['_f']) =>\n  !!fieldReference &&\n  !!fieldReference.validate &&\n  !!(\n    (isFunction(fieldReference.validate) &&\n      fieldReference.validate.constructor.name === ASYNC_FUNCTION) ||\n    (isObject(fieldReference.validate) &&\n      Object.values(fieldReference.validate).find(\n        (validateFunction: Validate<unknown, unknown>) =>\n          validateFunction.constructor.name === ASYNC_FUNCTION,\n      ))\n  );\n", "import type { InternalFieldName, Names } from '../types';\n\nexport default (\n  name: InternalFieldName,\n  _names: Names,\n  isBlurEvent?: boolean,\n) =>\n  !isBlurEvent &&\n  (_names.watchAll ||\n    _names.watch.has(name) ||\n    [..._names.watch].some(\n      (watchName) =>\n        name.startsWith(watchName) &&\n        /^\\.\\w+/.test(name.slice(watchName.length)),\n    ));\n", "import type { FieldRefs, InternalFieldName, Ref } from '../types';\nimport { get } from '../utils';\nimport isObject from '../utils/isObject';\n\nconst iterateFieldsByAction = (\n  fields: FieldRefs,\n  action: (ref: Ref, name: string) => 1 | undefined | void,\n  fieldsNames?: Set<InternalFieldName> | InternalFieldName[] | 0,\n  abortEarly?: boolean,\n) => {\n  for (const key of fieldsNames || Object.keys(fields)) {\n    const field = get(fields, key);\n\n    if (field) {\n      const { _f, ...currentField } = field;\n\n      if (_f) {\n        if (_f.refs && _f.refs[0] && action(_f.refs[0], key) && !abortEarly) {\n          return true;\n        } else if (_f.ref && action(_f.ref, _f.name) && !abortEarly) {\n          return true;\n        } else {\n          if (iterateFieldsByAction(currentField, action)) {\n            break;\n          }\n        }\n      } else if (isObject(currentField)) {\n        if (iterateFieldsByAction(currentField as FieldRefs, action)) {\n          break;\n        }\n      }\n    }\n  }\n  return;\n};\nexport default iterateFieldsByAction;\n", "import type { FieldError, FieldErrors, FieldValues } from '../types';\nimport get from '../utils/get';\nimport isKey from '../utils/isKey';\n\nexport default function schemaErrorLookup<T extends FieldValues = FieldValues>(\n  errors: FieldErrors<T>,\n  _fields: FieldValues,\n  name: string,\n): {\n  error?: FieldError;\n  name: string;\n} {\n  const error = get(errors, name);\n\n  if (error || isKey(name)) {\n    return {\n      error,\n      name,\n    };\n  }\n\n  const names = name.split('.');\n\n  while (names.length) {\n    const fieldName = names.join('.');\n    const field = get(_fields, fieldName);\n    const foundError = get(errors, fieldName);\n\n    if (field && !Array.isArray(field) && name !== fieldName) {\n      return { name };\n    }\n\n    if (foundError && foundError.type) {\n      return {\n        name: fieldName,\n        error: foundError,\n      };\n    }\n\n    if (foundError && foundError.root && foundError.root.type) {\n      return {\n        name: `${fieldName}.root`,\n        error: foundError.root,\n      };\n    }\n\n    names.pop();\n  }\n\n  return {\n    name,\n  };\n}\n", "import { VALIDATION_MODE } from '../constants';\nimport type {\n  FieldValues,\n  FormState,\n  InternalFieldName,\n  ReadFormState,\n} from '../types';\nimport isEmptyObject from '../utils/isEmptyObject';\n\nexport default <T extends FieldValues, K extends ReadFormState>(\n  formStateData: Partial<FormState<T>> & {\n    name?: InternalFieldName;\n    values?: T;\n  },\n  _proxyFormState: K,\n  updateFormState: (formState: Partial<FormState<T>>) => void,\n  isRoot?: boolean,\n) => {\n  updateFormState(formStateData);\n  const { name, ...formState } = formStateData;\n\n  return (\n    isEmptyObject(formState) ||\n    Object.keys(formState).length >= Object.keys(_proxyFormState).length ||\n    Object.keys(formState).find(\n      (key) =>\n        _proxyFormState[key as keyof ReadFormState] ===\n        (!isRoot || VALIDATION_MODE.all),\n    )\n  );\n};\n", "import type {\n  FieldError,\n  FieldErrors,\n  FieldValues,\n  InternalFieldName,\n} from '../types';\nimport convertToArrayPayload from '../utils/convertToArrayPayload';\nimport get from '../utils/get';\nimport set from '../utils/set';\n\nexport default <T extends FieldValues = FieldValues>(\n  errors: FieldErrors<T>,\n  error: Partial<Record<string, FieldError>>,\n  name: InternalFieldName,\n): FieldErrors<T> => {\n  const fieldArrayErrors = convertToArrayPayload(get(errors, name));\n  set(fieldArrayErrors, 'root', error[name]);\n  set(errors, name, fieldArrayErrors);\n  return errors;\n};\n", "import type { Message } from '../types';\nimport isString from '../utils/isString';\n\nexport default (value: unknown): value is Message => isString(value);\n", "import type { Field<PERSON>rror, Ref, ValidateResult } from '../types';\nimport isBoolean from '../utils/isBoolean';\nimport isMessage from '../utils/isMessage';\n\nexport default function getValidateError(\n  result: ValidateResult,\n  ref: Ref,\n  type = 'validate',\n): FieldError | void {\n  if (\n    isMessage(result) ||\n    (Array.isArray(result) && result.every(isMessage)) ||\n    (isBoolean(result) && !result)\n  ) {\n    return {\n      type,\n      message: isMessage(result) ? result : '',\n      ref,\n    };\n  }\n}\n", "import type { ValidationRule } from '../types';\nimport isObject from '../utils/isObject';\nimport isRegex from '../utils/isRegex';\n\nexport default (validationData?: ValidationRule) =>\n  isObject(validationData) && !isRegex(validationData)\n    ? validationData\n    : {\n        value: validationData,\n        message: '',\n      };\n", "import { INPUT_VALIDATION_RULES } from '../constants';\nimport type {\n  Field,\n  FieldError,\n  FieldValues,\n  InternalFieldErrors,\n  InternalNameSet,\n  MaxType,\n  Message,\n  MinType,\n  NativeFieldValue,\n} from '../types';\nimport get from '../utils/get';\nimport isBoolean from '../utils/isBoolean';\nimport isCheckBoxInput from '../utils/isCheckBoxInput';\nimport isEmptyObject from '../utils/isEmptyObject';\nimport isFileInput from '../utils/isFileInput';\nimport isFunction from '../utils/isFunction';\nimport isHTMLElement from '../utils/isHTMLElement';\nimport isMessage from '../utils/isMessage';\nimport isNullOrUndefined from '../utils/isNullOrUndefined';\nimport isObject from '../utils/isObject';\nimport isRadioInput from '../utils/isRadioInput';\nimport isRegex from '../utils/isRegex';\nimport isString from '../utils/isString';\nimport isUndefined from '../utils/isUndefined';\n\nimport appendErrors from './appendErrors';\nimport getCheckboxValue from './getCheckboxValue';\nimport getRadioValue from './getRadioValue';\nimport getValidateError from './getValidateError';\nimport getValueAndMessage from './getValueAndMessage';\n\nexport default async <T extends FieldValues>(\n  field: Field,\n  disabledFieldNames: InternalNameSet,\n  formValues: T,\n  validateAllFieldCriteria: boolean,\n  shouldUseNativeValidation?: boolean,\n  isFieldArray?: boolean,\n): Promise<InternalFieldErrors> => {\n  const {\n    ref,\n    refs,\n    required,\n    maxLength,\n    minLength,\n    min,\n    max,\n    pattern,\n    validate,\n    name,\n    valueAsNumber,\n    mount,\n  } = field._f;\n  const inputValue: NativeFieldValue = get(formValues, name);\n  if (!mount || disabledFieldNames.has(name)) {\n    return {};\n  }\n  const inputRef: HTMLInputElement = refs ? refs[0] : (ref as HTMLInputElement);\n  const setCustomValidity = (message?: string | boolean) => {\n    if (shouldUseNativeValidation && inputRef.reportValidity) {\n      inputRef.setCustomValidity(isBoolean(message) ? '' : message || '');\n      inputRef.reportValidity();\n    }\n  };\n  const error: InternalFieldErrors = {};\n  const isRadio = isRadioInput(ref);\n  const isCheckBox = isCheckBoxInput(ref);\n  const isRadioOrCheckbox = isRadio || isCheckBox;\n  const isEmpty =\n    ((valueAsNumber || isFileInput(ref)) &&\n      isUndefined(ref.value) &&\n      isUndefined(inputValue)) ||\n    (isHTMLElement(ref) && ref.value === '') ||\n    inputValue === '' ||\n    (Array.isArray(inputValue) && !inputValue.length);\n  const appendErrorsCurry = appendErrors.bind(\n    null,\n    name,\n    validateAllFieldCriteria,\n    error,\n  );\n  const getMinMaxMessage = (\n    exceedMax: boolean,\n    maxLengthMessage: Message,\n    minLengthMessage: Message,\n    maxType: MaxType = INPUT_VALIDATION_RULES.maxLength,\n    minType: MinType = INPUT_VALIDATION_RULES.minLength,\n  ) => {\n    const message = exceedMax ? maxLengthMessage : minLengthMessage;\n    error[name] = {\n      type: exceedMax ? maxType : minType,\n      message,\n      ref,\n      ...appendErrorsCurry(exceedMax ? maxType : minType, message),\n    };\n  };\n\n  if (\n    isFieldArray\n      ? !Array.isArray(inputValue) || !inputValue.length\n      : required &&\n        ((!isRadioOrCheckbox && (isEmpty || isNullOrUndefined(inputValue))) ||\n          (isBoolean(inputValue) && !inputValue) ||\n          (isCheckBox && !getCheckboxValue(refs).isValid) ||\n          (isRadio && !getRadioValue(refs).isValid))\n  ) {\n    const { value, message } = isMessage(required)\n      ? { value: !!required, message: required }\n      : getValueAndMessage(required);\n\n    if (value) {\n      error[name] = {\n        type: INPUT_VALIDATION_RULES.required,\n        message,\n        ref: inputRef,\n        ...appendErrorsCurry(INPUT_VALIDATION_RULES.required, message),\n      };\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(message);\n        return error;\n      }\n    }\n  }\n\n  if (!isEmpty && (!isNullOrUndefined(min) || !isNullOrUndefined(max))) {\n    let exceedMax;\n    let exceedMin;\n    const maxOutput = getValueAndMessage(max);\n    const minOutput = getValueAndMessage(min);\n\n    if (!isNullOrUndefined(inputValue) && !isNaN(inputValue as number)) {\n      const valueNumber =\n        (ref as HTMLInputElement).valueAsNumber ||\n        (inputValue ? +inputValue : inputValue);\n      if (!isNullOrUndefined(maxOutput.value)) {\n        exceedMax = valueNumber > maxOutput.value;\n      }\n      if (!isNullOrUndefined(minOutput.value)) {\n        exceedMin = valueNumber < minOutput.value;\n      }\n    } else {\n      const valueDate =\n        (ref as HTMLInputElement).valueAsDate || new Date(inputValue as string);\n      const convertTimeToDate = (time: unknown) =>\n        new Date(new Date().toDateString() + ' ' + time);\n      const isTime = ref.type == 'time';\n      const isWeek = ref.type == 'week';\n\n      if (isString(maxOutput.value) && inputValue) {\n        exceedMax = isTime\n          ? convertTimeToDate(inputValue) > convertTimeToDate(maxOutput.value)\n          : isWeek\n            ? inputValue > maxOutput.value\n            : valueDate > new Date(maxOutput.value);\n      }\n\n      if (isString(minOutput.value) && inputValue) {\n        exceedMin = isTime\n          ? convertTimeToDate(inputValue) < convertTimeToDate(minOutput.value)\n          : isWeek\n            ? inputValue < minOutput.value\n            : valueDate < new Date(minOutput.value);\n      }\n    }\n\n    if (exceedMax || exceedMin) {\n      getMinMaxMessage(\n        !!exceedMax,\n        maxOutput.message,\n        minOutput.message,\n        INPUT_VALIDATION_RULES.max,\n        INPUT_VALIDATION_RULES.min,\n      );\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(error[name]!.message);\n        return error;\n      }\n    }\n  }\n\n  if (\n    (maxLength || minLength) &&\n    !isEmpty &&\n    (isString(inputValue) || (isFieldArray && Array.isArray(inputValue)))\n  ) {\n    const maxLengthOutput = getValueAndMessage(maxLength);\n    const minLengthOutput = getValueAndMessage(minLength);\n    const exceedMax =\n      !isNullOrUndefined(maxLengthOutput.value) &&\n      inputValue.length > +maxLengthOutput.value;\n    const exceedMin =\n      !isNullOrUndefined(minLengthOutput.value) &&\n      inputValue.length < +minLengthOutput.value;\n\n    if (exceedMax || exceedMin) {\n      getMinMaxMessage(\n        exceedMax,\n        maxLengthOutput.message,\n        minLengthOutput.message,\n      );\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(error[name]!.message);\n        return error;\n      }\n    }\n  }\n\n  if (pattern && !isEmpty && isString(inputValue)) {\n    const { value: patternValue, message } = getValueAndMessage(pattern);\n\n    if (isRegex(patternValue) && !inputValue.match(patternValue)) {\n      error[name] = {\n        type: INPUT_VALIDATION_RULES.pattern,\n        message,\n        ref,\n        ...appendErrorsCurry(INPUT_VALIDATION_RULES.pattern, message),\n      };\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(message);\n        return error;\n      }\n    }\n  }\n\n  if (validate) {\n    if (isFunction(validate)) {\n      const result = await validate(inputValue, formValues);\n      const validateError = getValidateError(result, inputRef);\n\n      if (validateError) {\n        error[name] = {\n          ...validateError,\n          ...appendErrorsCurry(\n            INPUT_VALIDATION_RULES.validate,\n            validateError.message,\n          ),\n        };\n        if (!validateAllFieldCriteria) {\n          setCustomValidity(validateError.message);\n          return error;\n        }\n      }\n    } else if (isObject(validate)) {\n      let validationResult = {} as FieldError;\n\n      for (const key in validate) {\n        if (!isEmptyObject(validationResult) && !validateAllFieldCriteria) {\n          break;\n        }\n\n        const validateError = getValidateError(\n          await validate[key](inputValue, formValues),\n          inputRef,\n          key,\n        );\n\n        if (validateError) {\n          validationResult = {\n            ...validateError,\n            ...appendErrorsCurry(key, validateError.message),\n          };\n\n          setCustomValidity(validateError.message);\n\n          if (validateAllFieldCriteria) {\n            error[name] = validationResult;\n          }\n        }\n      }\n\n      if (!isEmptyObject(validationResult)) {\n        error[name] = {\n          ref: inputRef,\n          ...validationResult,\n        };\n        if (!validateAllFieldCriteria) {\n          return error;\n        }\n      }\n    }\n  }\n\n  setCustomValidity(true);\n  return error;\n};\n", "import { EVENTS, VALIDATION_MODE } from '../constants';\nimport type {\n  BatchField<PERSON>rrayUpdate,\n  ChangeHandler,\n  Control,\n  DeepPartial,\n  DelayCallback,\n  EventType,\n  Field,\n  FieldError,\n  FieldErrors,\n  FieldNamesMarkedBoolean,\n  FieldPath,\n  FieldRefs,\n  FieldValues,\n  FormState,\n  FromSubscribe,\n  GetIsDirty,\n  InternalFieldName,\n  Names,\n  Path,\n  ReadFormState,\n  Ref,\n  SetFieldValue,\n  SetValueConfig,\n  Subjects,\n  UseFormClearErrors,\n  UseFormGetFieldState,\n  UseFormGetValues,\n  UseFormHandleSubmit,\n  UseFormProps,\n  UseFormRegister,\n  UseFormReset,\n  UseFormResetField,\n  UseFormReturn,\n  UseFormSetError,\n  UseFormSetFocus,\n  UseFormSetValue,\n  UseFormSubscribe,\n  UseFormTrigger,\n  UseFormUnregister,\n  UseFormWatch,\n  WatchInternal,\n  WatchObserver,\n} from '../types';\nimport cloneObject from '../utils/cloneObject';\nimport compact from '../utils/compact';\nimport convertToArrayPayload from '../utils/convertToArrayPayload';\nimport createSubject from '../utils/createSubject';\nimport deepEqual from '../utils/deepEqual';\nimport get from '../utils/get';\nimport isBoolean from '../utils/isBoolean';\nimport isCheckBoxInput from '../utils/isCheckBoxInput';\nimport isDateObject from '../utils/isDateObject';\nimport isEmptyObject from '../utils/isEmptyObject';\nimport isFileInput from '../utils/isFileInput';\nimport isFunction from '../utils/isFunction';\nimport isHTMLElement from '../utils/isHTMLElement';\nimport isMultipleSelect from '../utils/isMultipleSelect';\nimport isNullOrUndefined from '../utils/isNullOrUndefined';\nimport isObject from '../utils/isObject';\nimport isRadioOrCheckbox from '../utils/isRadioOrCheckbox';\nimport isString from '../utils/isString';\nimport isUndefined from '../utils/isUndefined';\nimport isWeb from '../utils/isWeb';\nimport live from '../utils/live';\nimport set from '../utils/set';\nimport unset from '../utils/unset';\n\nimport generateWatchOutput from './generateWatchOutput';\nimport getDirtyFields from './getDirtyFields';\nimport getEventValue from './getEventValue';\nimport getFieldValue from './getFieldValue';\nimport getFieldValueAs from './getFieldValueAs';\nimport getResolverOptions from './getResolverOptions';\nimport getRuleValue from './getRuleValue';\nimport getValidationModes from './getValidationModes';\nimport hasPromiseValidation from './hasPromiseValidation';\nimport hasValidation from './hasValidation';\nimport isNameInFieldArray from './isNameInFieldArray';\nimport isWatched from './isWatched';\nimport iterateFieldsByAction from './iterateFieldsByAction';\nimport schemaErrorLookup from './schemaErrorLookup';\nimport shouldRenderFormState from './shouldRenderFormState';\nimport shouldSubscribeByName from './shouldSubscribeByName';\nimport skipValidation from './skipValidation';\nimport unsetEmptyArray from './unsetEmptyArray';\nimport updateFieldArrayRootError from './updateFieldArrayRootError';\nimport validateField from './validateField';\n\nconst defaultOptions = {\n  mode: VALIDATION_MODE.onSubmit,\n  reValidateMode: VALIDATION_MODE.onChange,\n  shouldFocusError: true,\n} as const;\n\nexport function createFormControl<\n  TFieldValues extends FieldValues = FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(\n  props: UseFormProps<TFieldValues, TContext, TTransformedValues> = {},\n): Omit<\n  UseFormReturn<TFieldValues, TContext, TTransformedValues>,\n  'formState'\n> & {\n  formControl: Omit<\n    UseFormReturn<TFieldValues, TContext, TTransformedValues>,\n    'formState'\n  >;\n} {\n  let _options = {\n    ...defaultOptions,\n    ...props,\n  };\n  let _formState: FormState<TFieldValues> = {\n    submitCount: 0,\n    isDirty: false,\n    isReady: false,\n    isLoading: isFunction(_options.defaultValues),\n    isValidating: false,\n    isSubmitted: false,\n    isSubmitting: false,\n    isSubmitSuccessful: false,\n    isValid: false,\n    touchedFields: {},\n    dirtyFields: {},\n    validatingFields: {},\n    errors: _options.errors || {},\n    disabled: _options.disabled || false,\n  };\n  let _fields: FieldRefs = {};\n  let _defaultValues =\n    isObject(_options.defaultValues) || isObject(_options.values)\n      ? cloneObject(_options.defaultValues || _options.values) || {}\n      : {};\n  let _formValues = _options.shouldUnregister\n    ? ({} as TFieldValues)\n    : (cloneObject(_defaultValues) as TFieldValues);\n  let _state = {\n    action: false,\n    mount: false,\n    watch: false,\n  };\n  let _names: Names = {\n    mount: new Set(),\n    disabled: new Set(),\n    unMount: new Set(),\n    array: new Set(),\n    watch: new Set(),\n  };\n  let delayErrorCallback: DelayCallback | null;\n  let timer = 0;\n  const _proxyFormState: ReadFormState = {\n    isDirty: false,\n    dirtyFields: false,\n    validatingFields: false,\n    touchedFields: false,\n    isValidating: false,\n    isValid: false,\n    errors: false,\n  };\n  let _proxySubscribeFormState = {\n    ..._proxyFormState,\n  };\n  const _subjects: Subjects<TFieldValues> = {\n    array: createSubject(),\n    state: createSubject(),\n  };\n\n  const shouldDisplayAllAssociatedErrors =\n    _options.criteriaMode === VALIDATION_MODE.all;\n\n  const debounce =\n    <T extends Function>(callback: T) =>\n    (wait: number) => {\n      clearTimeout(timer);\n      timer = setTimeout(callback, wait);\n    };\n\n  const _setValid = async (shouldUpdateValid?: boolean) => {\n    if (\n      !_options.disabled &&\n      (_proxyFormState.isValid ||\n        _proxySubscribeFormState.isValid ||\n        shouldUpdateValid)\n    ) {\n      const isValid = _options.resolver\n        ? isEmptyObject((await _runSchema()).errors)\n        : await executeBuiltInValidation(_fields, true);\n\n      if (isValid !== _formState.isValid) {\n        _subjects.state.next({\n          isValid,\n        });\n      }\n    }\n  };\n\n  const _updateIsValidating = (names?: string[], isValidating?: boolean) => {\n    if (\n      !_options.disabled &&\n      (_proxyFormState.isValidating ||\n        _proxyFormState.validatingFields ||\n        _proxySubscribeFormState.isValidating ||\n        _proxySubscribeFormState.validatingFields)\n    ) {\n      (names || Array.from(_names.mount)).forEach((name) => {\n        if (name) {\n          isValidating\n            ? set(_formState.validatingFields, name, isValidating)\n            : unset(_formState.validatingFields, name);\n        }\n      });\n\n      _subjects.state.next({\n        validatingFields: _formState.validatingFields,\n        isValidating: !isEmptyObject(_formState.validatingFields),\n      });\n    }\n  };\n\n  const _setFieldArray: BatchFieldArrayUpdate = (\n    name,\n    values = [],\n    method,\n    args,\n    shouldSetValues = true,\n    shouldUpdateFieldsAndState = true,\n  ) => {\n    if (args && method && !_options.disabled) {\n      _state.action = true;\n      if (shouldUpdateFieldsAndState && Array.isArray(get(_fields, name))) {\n        const fieldValues = method(get(_fields, name), args.argA, args.argB);\n        shouldSetValues && set(_fields, name, fieldValues);\n      }\n\n      if (\n        shouldUpdateFieldsAndState &&\n        Array.isArray(get(_formState.errors, name))\n      ) {\n        const errors = method(\n          get(_formState.errors, name),\n          args.argA,\n          args.argB,\n        );\n        shouldSetValues && set(_formState.errors, name, errors);\n        unsetEmptyArray(_formState.errors, name);\n      }\n\n      if (\n        (_proxyFormState.touchedFields ||\n          _proxySubscribeFormState.touchedFields) &&\n        shouldUpdateFieldsAndState &&\n        Array.isArray(get(_formState.touchedFields, name))\n      ) {\n        const touchedFields = method(\n          get(_formState.touchedFields, name),\n          args.argA,\n          args.argB,\n        );\n        shouldSetValues && set(_formState.touchedFields, name, touchedFields);\n      }\n\n      if (_proxyFormState.dirtyFields || _proxySubscribeFormState.dirtyFields) {\n        _formState.dirtyFields = getDirtyFields(_defaultValues, _formValues);\n      }\n\n      _subjects.state.next({\n        name,\n        isDirty: _getDirty(name, values),\n        dirtyFields: _formState.dirtyFields,\n        errors: _formState.errors,\n        isValid: _formState.isValid,\n      });\n    } else {\n      set(_formValues, name, values);\n    }\n  };\n\n  const updateErrors = (name: InternalFieldName, error: FieldError) => {\n    set(_formState.errors, name, error);\n    _subjects.state.next({\n      errors: _formState.errors,\n    });\n  };\n\n  const _setErrors = (errors: FieldErrors<TFieldValues>) => {\n    _formState.errors = errors;\n    _subjects.state.next({\n      errors: _formState.errors,\n      isValid: false,\n    });\n  };\n\n  const updateValidAndValue = (\n    name: InternalFieldName,\n    shouldSkipSetValueAs: boolean,\n    value?: unknown,\n    ref?: Ref,\n  ) => {\n    const field: Field = get(_fields, name);\n\n    if (field) {\n      const defaultValue = get(\n        _formValues,\n        name,\n        isUndefined(value) ? get(_defaultValues, name) : value,\n      );\n\n      isUndefined(defaultValue) ||\n      (ref && (ref as HTMLInputElement).defaultChecked) ||\n      shouldSkipSetValueAs\n        ? set(\n            _formValues,\n            name,\n            shouldSkipSetValueAs ? defaultValue : getFieldValue(field._f),\n          )\n        : setFieldValue(name, defaultValue);\n\n      _state.mount && _setValid();\n    }\n  };\n\n  const updateTouchAndDirty = (\n    name: InternalFieldName,\n    fieldValue: unknown,\n    isBlurEvent?: boolean,\n    shouldDirty?: boolean,\n    shouldRender?: boolean,\n  ): Partial<\n    Pick<FormState<TFieldValues>, 'dirtyFields' | 'isDirty' | 'touchedFields'>\n  > => {\n    let shouldUpdateField = false;\n    let isPreviousDirty = false;\n    const output: Partial<FormState<TFieldValues>> & { name: string } = {\n      name,\n    };\n\n    if (!_options.disabled) {\n      if (!isBlurEvent || shouldDirty) {\n        if (_proxyFormState.isDirty || _proxySubscribeFormState.isDirty) {\n          isPreviousDirty = _formState.isDirty;\n          _formState.isDirty = output.isDirty = _getDirty();\n          shouldUpdateField = isPreviousDirty !== output.isDirty;\n        }\n\n        const isCurrentFieldPristine = deepEqual(\n          get(_defaultValues, name),\n          fieldValue,\n        );\n\n        isPreviousDirty = !!get(_formState.dirtyFields, name);\n        isCurrentFieldPristine\n          ? unset(_formState.dirtyFields, name)\n          : set(_formState.dirtyFields, name, true);\n        output.dirtyFields = _formState.dirtyFields;\n        shouldUpdateField =\n          shouldUpdateField ||\n          ((_proxyFormState.dirtyFields ||\n            _proxySubscribeFormState.dirtyFields) &&\n            isPreviousDirty !== !isCurrentFieldPristine);\n      }\n\n      if (isBlurEvent) {\n        const isPreviousFieldTouched = get(_formState.touchedFields, name);\n\n        if (!isPreviousFieldTouched) {\n          set(_formState.touchedFields, name, isBlurEvent);\n          output.touchedFields = _formState.touchedFields;\n          shouldUpdateField =\n            shouldUpdateField ||\n            ((_proxyFormState.touchedFields ||\n              _proxySubscribeFormState.touchedFields) &&\n              isPreviousFieldTouched !== isBlurEvent);\n        }\n      }\n\n      shouldUpdateField && shouldRender && _subjects.state.next(output);\n    }\n\n    return shouldUpdateField ? output : {};\n  };\n\n  const shouldRenderByError = (\n    name: InternalFieldName,\n    isValid?: boolean,\n    error?: FieldError,\n    fieldState?: {\n      dirty?: FieldNamesMarkedBoolean<TFieldValues>;\n      isDirty?: boolean;\n      touched?: FieldNamesMarkedBoolean<TFieldValues>;\n    },\n  ) => {\n    const previousFieldError = get(_formState.errors, name);\n    const shouldUpdateValid =\n      (_proxyFormState.isValid || _proxySubscribeFormState.isValid) &&\n      isBoolean(isValid) &&\n      _formState.isValid !== isValid;\n\n    if (_options.delayError && error) {\n      delayErrorCallback = debounce(() => updateErrors(name, error));\n      delayErrorCallback(_options.delayError);\n    } else {\n      clearTimeout(timer);\n      delayErrorCallback = null;\n      error\n        ? set(_formState.errors, name, error)\n        : unset(_formState.errors, name);\n    }\n\n    if (\n      (error ? !deepEqual(previousFieldError, error) : previousFieldError) ||\n      !isEmptyObject(fieldState) ||\n      shouldUpdateValid\n    ) {\n      const updatedFormState = {\n        ...fieldState,\n        ...(shouldUpdateValid && isBoolean(isValid) ? { isValid } : {}),\n        errors: _formState.errors,\n        name,\n      };\n\n      _formState = {\n        ..._formState,\n        ...updatedFormState,\n      };\n\n      _subjects.state.next(updatedFormState);\n    }\n  };\n\n  const _runSchema = async (name?: InternalFieldName[]) => {\n    _updateIsValidating(name, true);\n    const result = await _options.resolver!(\n      _formValues as TFieldValues,\n      _options.context,\n      getResolverOptions(\n        name || _names.mount,\n        _fields,\n        _options.criteriaMode,\n        _options.shouldUseNativeValidation,\n      ),\n    );\n    _updateIsValidating(name);\n    return result;\n  };\n\n  const executeSchemaAndUpdateState = async (names?: InternalFieldName[]) => {\n    const { errors } = await _runSchema(names);\n\n    if (names) {\n      for (const name of names) {\n        const error = get(errors, name);\n        error\n          ? set(_formState.errors, name, error)\n          : unset(_formState.errors, name);\n      }\n    } else {\n      _formState.errors = errors;\n    }\n\n    return errors;\n  };\n\n  const executeBuiltInValidation = async (\n    fields: FieldRefs,\n    shouldOnlyCheckValid?: boolean,\n    context: {\n      valid: boolean;\n    } = {\n      valid: true,\n    },\n  ) => {\n    for (const name in fields) {\n      const field = fields[name];\n\n      if (field) {\n        const { _f, ...fieldValue } = field as Field;\n\n        if (_f) {\n          const isFieldArrayRoot = _names.array.has(_f.name);\n          const isPromiseFunction =\n            field._f && hasPromiseValidation((field as Field)._f);\n\n          if (isPromiseFunction && _proxyFormState.validatingFields) {\n            _updateIsValidating([name], true);\n          }\n\n          const fieldError = await validateField(\n            field as Field,\n            _names.disabled,\n            _formValues,\n            shouldDisplayAllAssociatedErrors,\n            _options.shouldUseNativeValidation && !shouldOnlyCheckValid,\n            isFieldArrayRoot,\n          );\n\n          if (isPromiseFunction && _proxyFormState.validatingFields) {\n            _updateIsValidating([name]);\n          }\n\n          if (fieldError[_f.name]) {\n            context.valid = false;\n            if (shouldOnlyCheckValid) {\n              break;\n            }\n          }\n\n          !shouldOnlyCheckValid &&\n            (get(fieldError, _f.name)\n              ? isFieldArrayRoot\n                ? updateFieldArrayRootError(\n                    _formState.errors,\n                    fieldError,\n                    _f.name,\n                  )\n                : set(_formState.errors, _f.name, fieldError[_f.name])\n              : unset(_formState.errors, _f.name));\n        }\n\n        !isEmptyObject(fieldValue) &&\n          (await executeBuiltInValidation(\n            fieldValue,\n            shouldOnlyCheckValid,\n            context,\n          ));\n      }\n    }\n\n    return context.valid;\n  };\n\n  const _removeUnmounted = () => {\n    for (const name of _names.unMount) {\n      const field: Field = get(_fields, name);\n\n      field &&\n        (field._f.refs\n          ? field._f.refs.every((ref) => !live(ref))\n          : !live(field._f.ref)) &&\n        unregister(name as FieldPath<TFieldValues>);\n    }\n\n    _names.unMount = new Set();\n  };\n\n  const _getDirty: GetIsDirty = (name, data) =>\n    !_options.disabled &&\n    (name && data && set(_formValues, name, data),\n    !deepEqual(getValues(), _defaultValues));\n\n  const _getWatch: WatchInternal<TFieldValues> = (\n    names,\n    defaultValue,\n    isGlobal,\n  ) =>\n    generateWatchOutput(\n      names,\n      _names,\n      {\n        ...(_state.mount\n          ? _formValues\n          : isUndefined(defaultValue)\n            ? _defaultValues\n            : isString(names)\n              ? { [names]: defaultValue }\n              : defaultValue),\n      },\n      isGlobal,\n      defaultValue,\n    );\n\n  const _getFieldArray = <TFieldArrayValues>(\n    name: InternalFieldName,\n  ): Partial<TFieldArrayValues>[] =>\n    compact(\n      get(\n        _state.mount ? _formValues : _defaultValues,\n        name,\n        _options.shouldUnregister ? get(_defaultValues, name, []) : [],\n      ),\n    );\n\n  const setFieldValue = (\n    name: InternalFieldName,\n    value: SetFieldValue<TFieldValues>,\n    options: SetValueConfig = {},\n  ) => {\n    const field: Field = get(_fields, name);\n    let fieldValue: unknown = value;\n\n    if (field) {\n      const fieldReference = field._f;\n\n      if (fieldReference) {\n        !fieldReference.disabled &&\n          set(_formValues, name, getFieldValueAs(value, fieldReference));\n\n        fieldValue =\n          isHTMLElement(fieldReference.ref) && isNullOrUndefined(value)\n            ? ''\n            : value;\n\n        if (isMultipleSelect(fieldReference.ref)) {\n          [...fieldReference.ref.options].forEach(\n            (optionRef) =>\n              (optionRef.selected = (\n                fieldValue as InternalFieldName[]\n              ).includes(optionRef.value)),\n          );\n        } else if (fieldReference.refs) {\n          if (isCheckBoxInput(fieldReference.ref)) {\n            fieldReference.refs.forEach((checkboxRef) => {\n              if (!checkboxRef.defaultChecked || !checkboxRef.disabled) {\n                if (Array.isArray(fieldValue)) {\n                  checkboxRef.checked = !!fieldValue.find(\n                    (data: string) => data === checkboxRef.value,\n                  );\n                } else {\n                  checkboxRef.checked =\n                    fieldValue === checkboxRef.value || !!fieldValue;\n                }\n              }\n            });\n          } else {\n            fieldReference.refs.forEach(\n              (radioRef: HTMLInputElement) =>\n                (radioRef.checked = radioRef.value === fieldValue),\n            );\n          }\n        } else if (isFileInput(fieldReference.ref)) {\n          fieldReference.ref.value = '';\n        } else {\n          fieldReference.ref.value = fieldValue;\n\n          if (!fieldReference.ref.type) {\n            _subjects.state.next({\n              name,\n              values: cloneObject(_formValues),\n            });\n          }\n        }\n      }\n    }\n\n    (options.shouldDirty || options.shouldTouch) &&\n      updateTouchAndDirty(\n        name,\n        fieldValue,\n        options.shouldTouch,\n        options.shouldDirty,\n        true,\n      );\n\n    options.shouldValidate && trigger(name as Path<TFieldValues>);\n  };\n\n  const setValues = <\n    T extends InternalFieldName,\n    K extends SetFieldValue<TFieldValues>,\n    U extends SetValueConfig,\n  >(\n    name: T,\n    value: K,\n    options: U,\n  ) => {\n    for (const fieldKey in value) {\n      if (!value.hasOwnProperty(fieldKey)) {\n        return;\n      }\n      const fieldValue = value[fieldKey];\n      const fieldName = name + '.' + fieldKey;\n      const field = get(_fields, fieldName);\n\n      (_names.array.has(name) ||\n        isObject(fieldValue) ||\n        (field && !field._f)) &&\n      !isDateObject(fieldValue)\n        ? setValues(fieldName, fieldValue, options)\n        : setFieldValue(fieldName, fieldValue, options);\n    }\n  };\n\n  const setValue: UseFormSetValue<TFieldValues> = (\n    name,\n    value,\n    options = {},\n  ) => {\n    const field = get(_fields, name);\n    const isFieldArray = _names.array.has(name);\n    const cloneValue = cloneObject(value);\n\n    set(_formValues, name, cloneValue);\n\n    if (isFieldArray) {\n      _subjects.array.next({\n        name,\n        values: cloneObject(_formValues),\n      });\n\n      if (\n        (_proxyFormState.isDirty ||\n          _proxyFormState.dirtyFields ||\n          _proxySubscribeFormState.isDirty ||\n          _proxySubscribeFormState.dirtyFields) &&\n        options.shouldDirty\n      ) {\n        _subjects.state.next({\n          name,\n          dirtyFields: getDirtyFields(_defaultValues, _formValues),\n          isDirty: _getDirty(name, cloneValue),\n        });\n      }\n    } else {\n      field && !field._f && !isNullOrUndefined(cloneValue)\n        ? setValues(name, cloneValue, options)\n        : setFieldValue(name, cloneValue, options);\n    }\n\n    isWatched(name, _names) && _subjects.state.next({ ..._formState });\n    _subjects.state.next({\n      name: _state.mount ? name : undefined,\n      values: cloneObject(_formValues),\n    });\n  };\n\n  const onChange: ChangeHandler = async (event) => {\n    _state.mount = true;\n    const target = event.target;\n    let name: string = target.name;\n    let isFieldValueUpdated = true;\n    const field: Field = get(_fields, name);\n    const _updateIsFieldValueUpdated = (fieldValue: unknown) => {\n      isFieldValueUpdated =\n        Number.isNaN(fieldValue) ||\n        (isDateObject(fieldValue) && isNaN(fieldValue.getTime())) ||\n        deepEqual(fieldValue, get(_formValues, name, fieldValue));\n    };\n    const validationModeBeforeSubmit = getValidationModes(_options.mode);\n    const validationModeAfterSubmit = getValidationModes(\n      _options.reValidateMode,\n    );\n\n    if (field) {\n      let error;\n      let isValid;\n      const fieldValue = target.type\n        ? getFieldValue(field._f)\n        : getEventValue(event);\n      const isBlurEvent =\n        event.type === EVENTS.BLUR || event.type === EVENTS.FOCUS_OUT;\n      const shouldSkipValidation =\n        (!hasValidation(field._f) &&\n          !_options.resolver &&\n          !get(_formState.errors, name) &&\n          !field._f.deps) ||\n        skipValidation(\n          isBlurEvent,\n          get(_formState.touchedFields, name),\n          _formState.isSubmitted,\n          validationModeAfterSubmit,\n          validationModeBeforeSubmit,\n        );\n      const watched = isWatched(name, _names, isBlurEvent);\n\n      set(_formValues, name, fieldValue);\n\n      if (isBlurEvent) {\n        field._f.onBlur && field._f.onBlur(event);\n        delayErrorCallback && delayErrorCallback(0);\n      } else if (field._f.onChange) {\n        field._f.onChange(event);\n      }\n\n      const fieldState = updateTouchAndDirty(name, fieldValue, isBlurEvent);\n\n      const shouldRender = !isEmptyObject(fieldState) || watched;\n\n      !isBlurEvent &&\n        _subjects.state.next({\n          name,\n          type: event.type,\n          values: cloneObject(_formValues),\n        });\n\n      if (shouldSkipValidation) {\n        if (_proxyFormState.isValid || _proxySubscribeFormState.isValid) {\n          if (_options.mode === 'onBlur') {\n            if (isBlurEvent) {\n              _setValid();\n            }\n          } else if (!isBlurEvent) {\n            _setValid();\n          }\n        }\n\n        return (\n          shouldRender &&\n          _subjects.state.next({ name, ...(watched ? {} : fieldState) })\n        );\n      }\n\n      !isBlurEvent && watched && _subjects.state.next({ ..._formState });\n\n      if (_options.resolver) {\n        const { errors } = await _runSchema([name]);\n\n        _updateIsFieldValueUpdated(fieldValue);\n\n        if (isFieldValueUpdated) {\n          const previousErrorLookupResult = schemaErrorLookup(\n            _formState.errors,\n            _fields,\n            name,\n          );\n          const errorLookupResult = schemaErrorLookup(\n            errors,\n            _fields,\n            previousErrorLookupResult.name || name,\n          );\n\n          error = errorLookupResult.error;\n          name = errorLookupResult.name;\n\n          isValid = isEmptyObject(errors);\n        }\n      } else {\n        _updateIsValidating([name], true);\n        error = (\n          await validateField(\n            field,\n            _names.disabled,\n            _formValues,\n            shouldDisplayAllAssociatedErrors,\n            _options.shouldUseNativeValidation,\n          )\n        )[name];\n        _updateIsValidating([name]);\n\n        _updateIsFieldValueUpdated(fieldValue);\n\n        if (isFieldValueUpdated) {\n          if (error) {\n            isValid = false;\n          } else if (\n            _proxyFormState.isValid ||\n            _proxySubscribeFormState.isValid\n          ) {\n            isValid = await executeBuiltInValidation(_fields, true);\n          }\n        }\n      }\n\n      if (isFieldValueUpdated) {\n        field._f.deps &&\n          trigger(\n            field._f.deps as\n              | FieldPath<TFieldValues>\n              | FieldPath<TFieldValues>[],\n          );\n        shouldRenderByError(name, isValid, error, fieldState);\n      }\n    }\n  };\n\n  const _focusInput = (ref: Ref, key: string) => {\n    if (get(_formState.errors, key) && ref.focus) {\n      ref.focus();\n      return 1;\n    }\n    return;\n  };\n\n  const trigger: UseFormTrigger<TFieldValues> = async (name, options = {}) => {\n    let isValid;\n    let validationResult;\n    const fieldNames = convertToArrayPayload(name) as InternalFieldName[];\n\n    if (_options.resolver) {\n      const errors = await executeSchemaAndUpdateState(\n        isUndefined(name) ? name : fieldNames,\n      );\n\n      isValid = isEmptyObject(errors);\n      validationResult = name\n        ? !fieldNames.some((name) => get(errors, name))\n        : isValid;\n    } else if (name) {\n      validationResult = (\n        await Promise.all(\n          fieldNames.map(async (fieldName) => {\n            const field = get(_fields, fieldName);\n            return await executeBuiltInValidation(\n              field && field._f ? { [fieldName]: field } : field,\n            );\n          }),\n        )\n      ).every(Boolean);\n      !(!validationResult && !_formState.isValid) && _setValid();\n    } else {\n      validationResult = isValid = await executeBuiltInValidation(_fields);\n    }\n\n    _subjects.state.next({\n      ...(!isString(name) ||\n      ((_proxyFormState.isValid || _proxySubscribeFormState.isValid) &&\n        isValid !== _formState.isValid)\n        ? {}\n        : { name }),\n      ...(_options.resolver || !name ? { isValid } : {}),\n      errors: _formState.errors,\n    });\n\n    options.shouldFocus &&\n      !validationResult &&\n      iterateFieldsByAction(\n        _fields,\n        _focusInput,\n        name ? fieldNames : _names.mount,\n      );\n\n    return validationResult;\n  };\n\n  const getValues: UseFormGetValues<TFieldValues> = (\n    fieldNames?:\n      | FieldPath<TFieldValues>\n      | ReadonlyArray<FieldPath<TFieldValues>>,\n  ) => {\n    const values = {\n      ...(_state.mount ? _formValues : _defaultValues),\n    };\n\n    return isUndefined(fieldNames)\n      ? values\n      : isString(fieldNames)\n        ? get(values, fieldNames)\n        : fieldNames.map((name) => get(values, name));\n  };\n\n  const getFieldState: UseFormGetFieldState<TFieldValues> = (\n    name,\n    formState,\n  ) => ({\n    invalid: !!get((formState || _formState).errors, name),\n    isDirty: !!get((formState || _formState).dirtyFields, name),\n    error: get((formState || _formState).errors, name),\n    isValidating: !!get(_formState.validatingFields, name),\n    isTouched: !!get((formState || _formState).touchedFields, name),\n  });\n\n  const clearErrors: UseFormClearErrors<TFieldValues> = (name) => {\n    name &&\n      convertToArrayPayload(name).forEach((inputName) =>\n        unset(_formState.errors, inputName),\n      );\n\n    _subjects.state.next({\n      errors: name ? _formState.errors : {},\n    });\n  };\n\n  const setError: UseFormSetError<TFieldValues> = (name, error, options) => {\n    const ref = (get(_fields, name, { _f: {} })._f || {}).ref;\n    const currentError = get(_formState.errors, name) || {};\n\n    // Don't override existing error messages elsewhere in the object tree.\n    const { ref: currentRef, message, type, ...restOfErrorTree } = currentError;\n\n    set(_formState.errors, name, {\n      ...restOfErrorTree,\n      ...error,\n      ref,\n    });\n\n    _subjects.state.next({\n      name,\n      errors: _formState.errors,\n      isValid: false,\n    });\n\n    options && options.shouldFocus && ref && ref.focus && ref.focus();\n  };\n\n  const watch: UseFormWatch<TFieldValues> = (\n    name?:\n      | FieldPath<TFieldValues>\n      | ReadonlyArray<FieldPath<TFieldValues>>\n      | WatchObserver<TFieldValues>,\n    defaultValue?: DeepPartial<TFieldValues>,\n  ) =>\n    isFunction(name)\n      ? _subjects.state.subscribe({\n          next: (payload) =>\n            name(\n              _getWatch(undefined, defaultValue),\n              payload as {\n                name?: FieldPath<TFieldValues>;\n                type?: EventType;\n                value?: unknown;\n              },\n            ),\n        })\n      : _getWatch(\n          name as InternalFieldName | InternalFieldName[],\n          defaultValue,\n          true,\n        );\n\n  const _subscribe: FromSubscribe<TFieldValues> = (props) =>\n    _subjects.state.subscribe({\n      next: (\n        formState: Partial<FormState<TFieldValues>> & {\n          name?: InternalFieldName;\n          values?: TFieldValues | undefined;\n          type?: EventType;\n        },\n      ) => {\n        if (\n          shouldSubscribeByName(props.name, formState.name, props.exact) &&\n          shouldRenderFormState(\n            formState,\n            (props.formState as ReadFormState) || _proxyFormState,\n            _setFormState,\n            props.reRenderRoot,\n          )\n        ) {\n          props.callback({\n            values: { ..._formValues } as TFieldValues,\n            ..._formState,\n            ...formState,\n          });\n        }\n      },\n    }).unsubscribe;\n\n  const subscribe: UseFormSubscribe<TFieldValues> = (props) => {\n    _state.mount = true;\n    _proxySubscribeFormState = {\n      ..._proxySubscribeFormState,\n      ...props.formState,\n    };\n    return _subscribe({\n      ...props,\n      formState: _proxySubscribeFormState,\n    });\n  };\n\n  const unregister: UseFormUnregister<TFieldValues> = (name, options = {}) => {\n    for (const fieldName of name ? convertToArrayPayload(name) : _names.mount) {\n      _names.mount.delete(fieldName);\n      _names.array.delete(fieldName);\n\n      if (!options.keepValue) {\n        unset(_fields, fieldName);\n        unset(_formValues, fieldName);\n      }\n\n      !options.keepError && unset(_formState.errors, fieldName);\n      !options.keepDirty && unset(_formState.dirtyFields, fieldName);\n      !options.keepTouched && unset(_formState.touchedFields, fieldName);\n      !options.keepIsValidating &&\n        unset(_formState.validatingFields, fieldName);\n      !_options.shouldUnregister &&\n        !options.keepDefaultValue &&\n        unset(_defaultValues, fieldName);\n    }\n\n    _subjects.state.next({\n      values: cloneObject(_formValues),\n    });\n\n    _subjects.state.next({\n      ..._formState,\n      ...(!options.keepDirty ? {} : { isDirty: _getDirty() }),\n    });\n\n    !options.keepIsValid && _setValid();\n  };\n\n  const _setDisabledField: Control<TFieldValues>['_setDisabledField'] = ({\n    disabled,\n    name,\n  }) => {\n    if (\n      (isBoolean(disabled) && _state.mount) ||\n      !!disabled ||\n      _names.disabled.has(name)\n    ) {\n      disabled ? _names.disabled.add(name) : _names.disabled.delete(name);\n    }\n  };\n\n  const register: UseFormRegister<TFieldValues> = (name, options = {}) => {\n    let field = get(_fields, name);\n    const disabledIsDefined =\n      isBoolean(options.disabled) || isBoolean(_options.disabled);\n\n    set(_fields, name, {\n      ...(field || {}),\n      _f: {\n        ...(field && field._f ? field._f : { ref: { name } }),\n        name,\n        mount: true,\n        ...options,\n      },\n    });\n    _names.mount.add(name);\n\n    if (field) {\n      _setDisabledField({\n        disabled: isBoolean(options.disabled)\n          ? options.disabled\n          : _options.disabled,\n        name,\n      });\n    } else {\n      updateValidAndValue(name, true, options.value);\n    }\n\n    return {\n      ...(disabledIsDefined\n        ? { disabled: options.disabled || _options.disabled }\n        : {}),\n      ...(_options.progressive\n        ? {\n            required: !!options.required,\n            min: getRuleValue(options.min),\n            max: getRuleValue(options.max),\n            minLength: getRuleValue<number>(options.minLength) as number,\n            maxLength: getRuleValue(options.maxLength) as number,\n            pattern: getRuleValue(options.pattern) as string,\n          }\n        : {}),\n      name,\n      onChange,\n      onBlur: onChange,\n      ref: (ref: HTMLInputElement | null): void => {\n        if (ref) {\n          register(name, options);\n          field = get(_fields, name);\n\n          const fieldRef = isUndefined(ref.value)\n            ? ref.querySelectorAll\n              ? (ref.querySelectorAll('input,select,textarea')[0] as Ref) || ref\n              : ref\n            : ref;\n          const radioOrCheckbox = isRadioOrCheckbox(fieldRef);\n          const refs = field._f.refs || [];\n\n          if (\n            radioOrCheckbox\n              ? refs.find((option: Ref) => option === fieldRef)\n              : fieldRef === field._f.ref\n          ) {\n            return;\n          }\n\n          set(_fields, name, {\n            _f: {\n              ...field._f,\n              ...(radioOrCheckbox\n                ? {\n                    refs: [\n                      ...refs.filter(live),\n                      fieldRef,\n                      ...(Array.isArray(get(_defaultValues, name)) ? [{}] : []),\n                    ],\n                    ref: { type: fieldRef.type, name },\n                  }\n                : { ref: fieldRef }),\n            },\n          });\n\n          updateValidAndValue(name, false, undefined, fieldRef);\n        } else {\n          field = get(_fields, name, {});\n\n          if (field._f) {\n            field._f.mount = false;\n          }\n\n          (_options.shouldUnregister || options.shouldUnregister) &&\n            !(isNameInFieldArray(_names.array, name) && _state.action) &&\n            _names.unMount.add(name);\n        }\n      },\n    };\n  };\n\n  const _focusError = () =>\n    _options.shouldFocusError &&\n    iterateFieldsByAction(_fields, _focusInput, _names.mount);\n\n  const _disableForm = (disabled?: boolean) => {\n    if (isBoolean(disabled)) {\n      _subjects.state.next({ disabled });\n      iterateFieldsByAction(\n        _fields,\n        (ref, name) => {\n          const currentField: Field = get(_fields, name);\n          if (currentField) {\n            ref.disabled = currentField._f.disabled || disabled;\n\n            if (Array.isArray(currentField._f.refs)) {\n              currentField._f.refs.forEach((inputRef) => {\n                inputRef.disabled = currentField._f.disabled || disabled;\n              });\n            }\n          }\n        },\n        0,\n        false,\n      );\n    }\n  };\n\n  const handleSubmit: UseFormHandleSubmit<TFieldValues, TTransformedValues> =\n    (onValid, onInvalid) => async (e) => {\n      let onValidError = undefined;\n      if (e) {\n        e.preventDefault && e.preventDefault();\n        (e as React.BaseSyntheticEvent).persist &&\n          (e as React.BaseSyntheticEvent).persist();\n      }\n      let fieldValues: TFieldValues | TTransformedValues | {} =\n        cloneObject(_formValues);\n\n      _subjects.state.next({\n        isSubmitting: true,\n      });\n\n      if (_options.resolver) {\n        const { errors, values } = await _runSchema();\n        _formState.errors = errors;\n        fieldValues = cloneObject(values) as TFieldValues;\n      } else {\n        await executeBuiltInValidation(_fields);\n      }\n\n      if (_names.disabled.size) {\n        for (const name of _names.disabled) {\n          unset(fieldValues, name);\n        }\n      }\n\n      unset(_formState.errors, 'root');\n\n      if (isEmptyObject(_formState.errors)) {\n        _subjects.state.next({\n          errors: {},\n        });\n        try {\n          await onValid(fieldValues as TTransformedValues, e);\n        } catch (error) {\n          onValidError = error;\n        }\n      } else {\n        if (onInvalid) {\n          await onInvalid({ ..._formState.errors }, e);\n        }\n        _focusError();\n        setTimeout(_focusError);\n      }\n\n      _subjects.state.next({\n        isSubmitted: true,\n        isSubmitting: false,\n        isSubmitSuccessful: isEmptyObject(_formState.errors) && !onValidError,\n        submitCount: _formState.submitCount + 1,\n        errors: _formState.errors,\n      });\n      if (onValidError) {\n        throw onValidError;\n      }\n    };\n\n  const resetField: UseFormResetField<TFieldValues> = (name, options = {}) => {\n    if (get(_fields, name)) {\n      if (isUndefined(options.defaultValue)) {\n        setValue(name, cloneObject(get(_defaultValues, name)));\n      } else {\n        setValue(\n          name,\n          options.defaultValue as Parameters<typeof setValue<typeof name>>[1],\n        );\n        set(_defaultValues, name, cloneObject(options.defaultValue));\n      }\n\n      if (!options.keepTouched) {\n        unset(_formState.touchedFields, name);\n      }\n\n      if (!options.keepDirty) {\n        unset(_formState.dirtyFields, name);\n        _formState.isDirty = options.defaultValue\n          ? _getDirty(name, cloneObject(get(_defaultValues, name)))\n          : _getDirty();\n      }\n\n      if (!options.keepError) {\n        unset(_formState.errors, name);\n        _proxyFormState.isValid && _setValid();\n      }\n\n      _subjects.state.next({ ..._formState });\n    }\n  };\n\n  const _reset: UseFormReset<TFieldValues> = (\n    formValues,\n    keepStateOptions = {},\n  ) => {\n    const updatedValues = formValues ? cloneObject(formValues) : _defaultValues;\n    const cloneUpdatedValues = cloneObject(updatedValues);\n    const isEmptyResetValues = isEmptyObject(formValues);\n    const values = isEmptyResetValues ? _defaultValues : cloneUpdatedValues;\n\n    if (!keepStateOptions.keepDefaultValues) {\n      _defaultValues = updatedValues;\n    }\n\n    if (!keepStateOptions.keepValues) {\n      if (keepStateOptions.keepDirtyValues) {\n        const fieldsToCheck = new Set([\n          ..._names.mount,\n          ...Object.keys(getDirtyFields(_defaultValues, _formValues)),\n        ]);\n        for (const fieldName of Array.from(fieldsToCheck)) {\n          get(_formState.dirtyFields, fieldName)\n            ? set(values, fieldName, get(_formValues, fieldName))\n            : setValue(\n                fieldName as FieldPath<TFieldValues>,\n                get(values, fieldName),\n              );\n        }\n      } else {\n        if (isWeb && isUndefined(formValues)) {\n          for (const name of _names.mount) {\n            const field = get(_fields, name);\n            if (field && field._f) {\n              const fieldReference = Array.isArray(field._f.refs)\n                ? field._f.refs[0]\n                : field._f.ref;\n\n              if (isHTMLElement(fieldReference)) {\n                const form = fieldReference.closest('form');\n                if (form) {\n                  form.reset();\n                  break;\n                }\n              }\n            }\n          }\n        }\n\n        if (keepStateOptions.keepFieldsRef) {\n          for (const fieldName of _names.mount) {\n            setValue(\n              fieldName as FieldPath<TFieldValues>,\n              get(values, fieldName),\n            );\n          }\n        } else {\n          _fields = {};\n        }\n      }\n\n      _formValues = _options.shouldUnregister\n        ? keepStateOptions.keepDefaultValues\n          ? (cloneObject(_defaultValues) as TFieldValues)\n          : ({} as TFieldValues)\n        : (cloneObject(values) as TFieldValues);\n\n      _subjects.array.next({\n        values: { ...values },\n      });\n\n      _subjects.state.next({\n        values: { ...values } as TFieldValues,\n      });\n    }\n\n    _names = {\n      mount: keepStateOptions.keepDirtyValues ? _names.mount : new Set(),\n      unMount: new Set(),\n      array: new Set(),\n      disabled: new Set(),\n      watch: new Set(),\n      watchAll: false,\n      focus: '',\n    };\n\n    _state.mount =\n      !_proxyFormState.isValid ||\n      !!keepStateOptions.keepIsValid ||\n      !!keepStateOptions.keepDirtyValues;\n\n    _state.watch = !!_options.shouldUnregister;\n\n    _subjects.state.next({\n      submitCount: keepStateOptions.keepSubmitCount\n        ? _formState.submitCount\n        : 0,\n      isDirty: isEmptyResetValues\n        ? false\n        : keepStateOptions.keepDirty\n          ? _formState.isDirty\n          : !!(\n              keepStateOptions.keepDefaultValues &&\n              !deepEqual(formValues, _defaultValues)\n            ),\n      isSubmitted: keepStateOptions.keepIsSubmitted\n        ? _formState.isSubmitted\n        : false,\n      dirtyFields: isEmptyResetValues\n        ? {}\n        : keepStateOptions.keepDirtyValues\n          ? keepStateOptions.keepDefaultValues && _formValues\n            ? getDirtyFields(_defaultValues, _formValues)\n            : _formState.dirtyFields\n          : keepStateOptions.keepDefaultValues && formValues\n            ? getDirtyFields(_defaultValues, formValues)\n            : keepStateOptions.keepDirty\n              ? _formState.dirtyFields\n              : {},\n      touchedFields: keepStateOptions.keepTouched\n        ? _formState.touchedFields\n        : {},\n      errors: keepStateOptions.keepErrors ? _formState.errors : {},\n      isSubmitSuccessful: keepStateOptions.keepIsSubmitSuccessful\n        ? _formState.isSubmitSuccessful\n        : false,\n      isSubmitting: false,\n    });\n  };\n\n  const reset: UseFormReset<TFieldValues> = (formValues, keepStateOptions) =>\n    _reset(\n      isFunction(formValues)\n        ? (formValues as Function)(_formValues as TFieldValues)\n        : formValues,\n      keepStateOptions,\n    );\n\n  const setFocus: UseFormSetFocus<TFieldValues> = (name, options = {}) => {\n    const field = get(_fields, name);\n    const fieldReference = field && field._f;\n\n    if (fieldReference) {\n      const fieldRef = fieldReference.refs\n        ? fieldReference.refs[0]\n        : fieldReference.ref;\n\n      if (fieldRef.focus) {\n        fieldRef.focus();\n        options.shouldSelect &&\n          isFunction(fieldRef.select) &&\n          fieldRef.select();\n      }\n    }\n  };\n\n  const _setFormState = (\n    updatedFormState: Partial<FormState<TFieldValues>>,\n  ) => {\n    _formState = {\n      ..._formState,\n      ...updatedFormState,\n    };\n  };\n\n  const _resetDefaultValues = () =>\n    isFunction(_options.defaultValues) &&\n    (_options.defaultValues as Function)().then((values: TFieldValues) => {\n      reset(values, _options.resetOptions);\n      _subjects.state.next({\n        isLoading: false,\n      });\n    });\n\n  const methods = {\n    control: {\n      register,\n      unregister,\n      getFieldState,\n      handleSubmit,\n      setError,\n      _subscribe,\n      _runSchema,\n      _focusError,\n      _getWatch,\n      _getDirty,\n      _setValid,\n      _setFieldArray,\n      _setDisabledField,\n      _setErrors,\n      _getFieldArray,\n      _reset,\n      _resetDefaultValues,\n      _removeUnmounted,\n      _disableForm,\n      _subjects,\n      _proxyFormState,\n      get _fields() {\n        return _fields;\n      },\n      get _formValues() {\n        return _formValues;\n      },\n      get _state() {\n        return _state;\n      },\n      set _state(value) {\n        _state = value;\n      },\n      get _defaultValues() {\n        return _defaultValues;\n      },\n      get _names() {\n        return _names;\n      },\n      set _names(value) {\n        _names = value;\n      },\n      get _formState() {\n        return _formState;\n      },\n      get _options() {\n        return _options;\n      },\n      set _options(value) {\n        _options = {\n          ..._options,\n          ...value,\n        };\n      },\n    },\n    subscribe,\n    trigger,\n    register,\n    handleSubmit,\n    watch,\n    setValue,\n    getValues,\n    reset,\n    resetField,\n    clearErrors,\n    unregister,\n    setError,\n    setFocus,\n    getFieldState,\n  };\n\n  return {\n    ...methods,\n    formControl: methods,\n  };\n}\n", "import type { Field } from '../types';\n\nexport default (options: Field['_f']) =>\n  options.mount &&\n  (options.required ||\n    options.min ||\n    options.max ||\n    options.maxLength ||\n    options.minLength ||\n    options.pattern ||\n    options.validate);\n", "import type { ValidationModeFlags } from '../types';\n\nexport default (\n  isBlurEvent: boolean,\n  isTouched: boolean,\n  isSubmitted: boolean,\n  reValidateMode: {\n    isOnBlur: boolean;\n    isOnChange: boolean;\n  },\n  mode: Partial<ValidationModeFlags>,\n) => {\n  if (mode.isOnAll) {\n    return false;\n  } else if (!isSubmitted && mode.isOnTouch) {\n    return !(isTouched || isBlurEvent);\n  } else if (isSubmitted ? reValidateMode.isOnBlur : mode.isOnBlur) {\n    return !isBlurEvent;\n  } else if (isSubmitted ? reValidateMode.isOnChange : mode.isOnChange) {\n    return isBlurEvent;\n  }\n  return true;\n};\n", "import convertToArrayPayload from '../utils/convertToArrayPayload';\n\nexport default <T extends string | readonly string[] | undefined>(\n  name?: T,\n  signalName?: string,\n  exact?: boolean,\n) =>\n  !name ||\n  !signalName ||\n  name === signalName ||\n  convertToArrayPayload(name).some(\n    (currentName) =>\n      currentName &&\n      (exact\n        ? currentName === signalName\n        : currentName.startsWith(signalName) ||\n          signalName.startsWith(currentName)),\n  );\n", "import type { FieldElement } from '../types';\n\nimport isCheckBoxInput from './isCheckBoxInput';\nimport isRadioInput from './isRadioInput';\n\nexport default (ref: FieldElement): ref is HTMLInputElement =>\n  isRadioInput(ref) || isCheckBoxInput(ref);\n", "import compact from '../utils/compact';\nimport get from '../utils/get';\nimport unset from '../utils/unset';\n\nexport default <T>(ref: T, name: string) =>\n  !compact(get(ref, name)).length && unset(ref, name);\n", "export default () => {\n  if (typeof crypto !== 'undefined' && crypto.randomUUID) {\n    return crypto.randomUUID();\n  }\n\n  const d =\n    typeof performance === 'undefined' ? Date.now() : performance.now() * 1000;\n\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\n    const r = (Math.random() * 16 + d) % 16 | 0;\n\n    return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16);\n  });\n};\n", "import type { FieldArrayMethodProps, InternalFieldName } from '../types';\nimport isUndefined from '../utils/isUndefined';\n\nexport default (\n  name: InternalFieldName,\n  index: number,\n  options: FieldArrayMethodProps = {},\n): string =>\n  options.shouldFocus || isUndefined(options.shouldFocus)\n    ? options.focusName ||\n      `${name}.${isUndefined(options.focusIndex) ? index : options.focusIndex}.`\n    : '';\n", "import convertToArrayPayload from './convertToArrayPayload';\n\nexport default <T>(data: T[], value: T | T[]): T[] => [\n  ...data,\n  ...convertToArrayPayload(value),\n];\n", "export default <T>(value: T | T[]): undefined[] | undefined =>\n  Array.isArray(value) ? value.map(() => undefined) : undefined;\n", "import convertToArrayPayload from './convertToArrayPayload';\n\nexport default function insert<T>(data: T[], index: number): (T | undefined)[];\nexport default function insert<T>(\n  data: T[],\n  index: number,\n  value: T | T[],\n): T[];\nexport default function insert<T>(\n  data: T[],\n  index: number,\n  value?: T | T[],\n): (T | undefined)[] {\n  return [\n    ...data.slice(0, index),\n    ...convertToArrayPayload(value),\n    ...data.slice(index),\n  ];\n}\n", "import isUndefined from './isUndefined';\n\nexport default <T>(\n  data: (T | undefined)[],\n  from: number,\n  to: number,\n): (T | undefined)[] => {\n  if (!Array.isArray(data)) {\n    return [];\n  }\n\n  if (isUndefined(data[to])) {\n    data[to] = undefined;\n  }\n  data.splice(to, 0, data.splice(from, 1)[0]);\n\n  return data;\n};\n", "import convertToArrayPayload from './convertToArrayPayload';\n\nexport default <T>(data: T[], value: T | T[]): T[] => [\n  ...convertToArrayPayload(value),\n  ...convertToArrayPayload(data),\n];\n", "import compact from './compact';\nimport convertToArrayPayload from './convertToArrayPayload';\nimport isUndefined from './isUndefined';\n\nfunction removeAtIndexes<T>(data: T[], indexes: number[]): T[] {\n  let i = 0;\n  const temp = [...data];\n\n  for (const index of indexes) {\n    temp.splice(index - i, 1);\n    i++;\n  }\n\n  return compact(temp).length ? temp : [];\n}\n\nexport default <T>(data: T[], index?: number | number[]): T[] =>\n  isUndefined(index)\n    ? []\n    : removeAtIndexes(\n        data,\n        (convertToArrayPayload(index) as number[]).sort((a, b) => a - b),\n      );\n", "export default <T>(data: T[], indexA: number, indexB: number): void => {\n  [data[indexA], data[indexB]] = [data[indexB], data[indexA]];\n};\n", "export default <T>(fieldValues: T[], index: number, value: T) => {\n  fieldValues[index] = value;\n  return fieldValues;\n};\n", "import React from 'react';\n\nimport generateId from './logic/generateId';\nimport getFocusFieldName from './logic/getFocusFieldName';\nimport getValidationModes from './logic/getValidationModes';\nimport isWatched from './logic/isWatched';\nimport iterateFieldsByAction from './logic/iterateFieldsByAction';\nimport updateFieldArrayRootError from './logic/updateFieldArrayRootError';\nimport validateField from './logic/validateField';\nimport appendAt from './utils/append';\nimport cloneObject from './utils/cloneObject';\nimport convertToArrayPayload from './utils/convertToArrayPayload';\nimport fillEmptyArray from './utils/fillEmptyArray';\nimport get from './utils/get';\nimport insertAt from './utils/insert';\nimport isEmptyObject from './utils/isEmptyObject';\nimport moveArrayAt from './utils/move';\nimport prependAt from './utils/prepend';\nimport removeArrayAt from './utils/remove';\nimport set from './utils/set';\nimport swapArrayAt from './utils/swap';\nimport unset from './utils/unset';\nimport updateAt from './utils/update';\nimport { VALIDATION_MODE } from './constants';\nimport type {\n  Control,\n  Field,\n  FieldArray,\n  FieldArrayMethodProps,\n  FieldArrayPath,\n  FieldArrayWithId,\n  FieldErrors,\n  FieldPath,\n  FieldValues,\n  FormState,\n  InternalFieldName,\n  RegisterOptions,\n  UseFieldArrayProps,\n  UseFieldArrayReturn,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect';\n\n/**\n * A custom hook that exposes convenient methods to perform operations with a list of dynamic inputs that need to be appended, updated, removed etc. • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn) • [Video](https://youtu.be/4MrbfGSFY2A)\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usefieldarray) • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn)\n *\n * @param props - useFieldArray props\n *\n * @returns methods - functions to manipulate with the Field Arrays (dynamic inputs) {@link UseFieldArrayReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, control, handleSubmit, reset, trigger, setError } = useForm({\n *     defaultValues: {\n *       test: []\n *     }\n *   });\n *   const { fields, append } = useFieldArray({\n *     control,\n *     name: \"test\"\n *   });\n *\n *   return (\n *     <form onSubmit={handleSubmit(data => console.log(data))}>\n *       {fields.map((item, index) => (\n *          <input key={item.id} {...register(`test.${index}.firstName`)}  />\n *       ))}\n *       <button type=\"button\" onClick={() => append({ firstName: \"bill\" })}>\n *         append\n *       </button>\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nexport function useFieldArray<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldArrayName extends\n    FieldArrayPath<TFieldValues> = FieldArrayPath<TFieldValues>,\n  TKeyName extends string = 'id',\n  TTransformedValues = TFieldValues,\n>(\n  props: UseFieldArrayProps<\n    TFieldValues,\n    TFieldArrayName,\n    TKeyName,\n    TTransformedValues\n  >,\n): UseFieldArrayReturn<TFieldValues, TFieldArrayName, TKeyName> {\n  const methods = useFormContext();\n  const {\n    control = methods.control,\n    name,\n    keyName = 'id',\n    shouldUnregister,\n    rules,\n  } = props;\n  const [fields, setFields] = React.useState(control._getFieldArray(name));\n  const ids = React.useRef<string[]>(\n    control._getFieldArray(name).map(generateId),\n  );\n  const _fieldIds = React.useRef(fields);\n  const _name = React.useRef(name);\n  const _actioned = React.useRef(false);\n\n  _name.current = name;\n  _fieldIds.current = fields;\n  control._names.array.add(name);\n\n  rules &&\n    (control as Control<TFieldValues, any, TTransformedValues>).register(\n      name as FieldPath<TFieldValues>,\n      rules as RegisterOptions<TFieldValues>,\n    );\n\n  useIsomorphicLayoutEffect(\n    () =>\n      control._subjects.array.subscribe({\n        next: ({\n          values,\n          name: fieldArrayName,\n        }: {\n          values?: FieldValues;\n          name?: InternalFieldName;\n        }) => {\n          if (fieldArrayName === _name.current || !fieldArrayName) {\n            const fieldValues = get(values, _name.current);\n            if (Array.isArray(fieldValues)) {\n              setFields(fieldValues);\n              ids.current = fieldValues.map(generateId);\n            }\n          }\n        },\n      }).unsubscribe,\n    [control],\n  );\n\n  const updateValues = React.useCallback(\n    <\n      T extends Partial<\n        FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>\n      >[],\n    >(\n      updatedFieldArrayValues: T,\n    ) => {\n      _actioned.current = true;\n      control._setFieldArray(name, updatedFieldArrayValues);\n    },\n    [control, name],\n  );\n\n  const append = (\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n    options?: FieldArrayMethodProps,\n  ) => {\n    const appendValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = appendAt(\n      control._getFieldArray(name),\n      appendValue,\n    );\n    control._names.focus = getFocusFieldName(\n      name,\n      updatedFieldArrayValues.length - 1,\n      options,\n    );\n    ids.current = appendAt(ids.current, appendValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, appendAt, {\n      argA: fillEmptyArray(value),\n    });\n  };\n\n  const prepend = (\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n    options?: FieldArrayMethodProps,\n  ) => {\n    const prependValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = prependAt(\n      control._getFieldArray(name),\n      prependValue,\n    );\n    control._names.focus = getFocusFieldName(name, 0, options);\n    ids.current = prependAt(ids.current, prependValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, prependAt, {\n      argA: fillEmptyArray(value),\n    });\n  };\n\n  const remove = (index?: number | number[]) => {\n    const updatedFieldArrayValues: Partial<\n      FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>\n    >[] = removeArrayAt(control._getFieldArray(name), index);\n    ids.current = removeArrayAt(ids.current, index);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    !Array.isArray(get(control._fields, name)) &&\n      set(control._fields, name, undefined);\n    control._setFieldArray(name, updatedFieldArrayValues, removeArrayAt, {\n      argA: index,\n    });\n  };\n\n  const insert = (\n    index: number,\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n    options?: FieldArrayMethodProps,\n  ) => {\n    const insertValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = insertAt(\n      control._getFieldArray(name),\n      index,\n      insertValue,\n    );\n    control._names.focus = getFocusFieldName(name, index, options);\n    ids.current = insertAt(ids.current, index, insertValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(name, updatedFieldArrayValues, insertAt, {\n      argA: index,\n      argB: fillEmptyArray(value),\n    });\n  };\n\n  const swap = (indexA: number, indexB: number) => {\n    const updatedFieldArrayValues = control._getFieldArray(name);\n    swapArrayAt(updatedFieldArrayValues, indexA, indexB);\n    swapArrayAt(ids.current, indexA, indexB);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(\n      name,\n      updatedFieldArrayValues,\n      swapArrayAt,\n      {\n        argA: indexA,\n        argB: indexB,\n      },\n      false,\n    );\n  };\n\n  const move = (from: number, to: number) => {\n    const updatedFieldArrayValues = control._getFieldArray(name);\n    moveArrayAt(updatedFieldArrayValues, from, to);\n    moveArrayAt(ids.current, from, to);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._setFieldArray(\n      name,\n      updatedFieldArrayValues,\n      moveArrayAt,\n      {\n        argA: from,\n        argB: to,\n      },\n      false,\n    );\n  };\n\n  const update = (\n    index: number,\n    value: FieldArray<TFieldValues, TFieldArrayName>,\n  ) => {\n    const updateValue = cloneObject(value);\n    const updatedFieldArrayValues = updateAt(\n      control._getFieldArray<\n        FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>\n      >(name),\n      index,\n      updateValue as FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>,\n    );\n    ids.current = [...updatedFieldArrayValues].map((item, i) =>\n      !item || i === index ? generateId() : ids.current[i],\n    );\n    updateValues(updatedFieldArrayValues);\n    setFields([...updatedFieldArrayValues]);\n    control._setFieldArray(\n      name,\n      updatedFieldArrayValues,\n      updateAt,\n      {\n        argA: index,\n        argB: updateValue,\n      },\n      true,\n      false,\n    );\n  };\n\n  const replace = (\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n  ) => {\n    const updatedFieldArrayValues = convertToArrayPayload(cloneObject(value));\n    ids.current = updatedFieldArrayValues.map(generateId);\n    updateValues([...updatedFieldArrayValues]);\n    setFields([...updatedFieldArrayValues]);\n    control._setFieldArray(\n      name,\n      [...updatedFieldArrayValues],\n      <T>(data: T): T => data,\n      {},\n      true,\n      false,\n    );\n  };\n\n  React.useEffect(() => {\n    control._state.action = false;\n\n    isWatched(name, control._names) &&\n      control._subjects.state.next({\n        ...control._formState,\n      } as FormState<TFieldValues>);\n\n    if (\n      _actioned.current &&\n      (!getValidationModes(control._options.mode).isOnSubmit ||\n        control._formState.isSubmitted) &&\n      !getValidationModes(control._options.reValidateMode).isOnSubmit\n    ) {\n      if (control._options.resolver) {\n        control._runSchema([name]).then((result) => {\n          const error = get(result.errors, name);\n          const existingError = get(control._formState.errors, name);\n\n          if (\n            existingError\n              ? (!error && existingError.type) ||\n                (error &&\n                  (existingError.type !== error.type ||\n                    existingError.message !== error.message))\n              : error && error.type\n          ) {\n            error\n              ? set(control._formState.errors, name, error)\n              : unset(control._formState.errors, name);\n            control._subjects.state.next({\n              errors: control._formState.errors as FieldErrors<TFieldValues>,\n            });\n          }\n        });\n      } else {\n        const field: Field = get(control._fields, name);\n        if (\n          field &&\n          field._f &&\n          !(\n            getValidationModes(control._options.reValidateMode).isOnSubmit &&\n            getValidationModes(control._options.mode).isOnSubmit\n          )\n        ) {\n          validateField(\n            field,\n            control._names.disabled,\n            control._formValues,\n            control._options.criteriaMode === VALIDATION_MODE.all,\n            control._options.shouldUseNativeValidation,\n            true,\n          ).then(\n            (error) =>\n              !isEmptyObject(error) &&\n              control._subjects.state.next({\n                errors: updateFieldArrayRootError(\n                  control._formState.errors as FieldErrors<TFieldValues>,\n                  error,\n                  name,\n                ) as FieldErrors<TFieldValues>,\n              }),\n          );\n        }\n      }\n    }\n\n    control._subjects.state.next({\n      name,\n      values: cloneObject(control._formValues) as TFieldValues,\n    });\n\n    control._names.focus &&\n      iterateFieldsByAction(control._fields, (ref, key: string) => {\n        if (\n          control._names.focus &&\n          key.startsWith(control._names.focus) &&\n          ref.focus\n        ) {\n          ref.focus();\n          return 1;\n        }\n        return;\n      });\n\n    control._names.focus = '';\n\n    control._setValid();\n    _actioned.current = false;\n  }, [fields, name, control]);\n\n  React.useEffect(() => {\n    !get(control._formValues, name) && control._setFieldArray(name);\n\n    return () => {\n      const updateMounted = (name: InternalFieldName, value: boolean) => {\n        const field: Field = get(control._fields, name);\n        if (field && field._f) {\n          field._f.mount = value;\n        }\n      };\n\n      control._options.shouldUnregister || shouldUnregister\n        ? control.unregister(name as FieldPath<TFieldValues>)\n        : updateMounted(name, false);\n    };\n  }, [name, control, keyName, shouldUnregister]);\n\n  return {\n    swap: React.useCallback(swap, [updateValues, name, control]),\n    move: React.useCallback(move, [updateValues, name, control]),\n    prepend: React.useCallback(prepend, [updateValues, name, control]),\n    append: React.useCallback(append, [updateValues, name, control]),\n    remove: React.useCallback(remove, [updateValues, name, control]),\n    insert: React.useCallback(insert, [updateValues, name, control]),\n    update: React.useCallback(update, [updateValues, name, control]),\n    replace: React.useCallback(replace, [updateValues, name, control]),\n    fields: React.useMemo(\n      () =>\n        fields.map((field, index) => ({\n          ...field,\n          [keyName]: ids.current[index] || generateId(),\n        })) as FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>[],\n      [fields, keyName],\n    ),\n  };\n}\n", "import React from 'react';\n\nimport getProxyFormState from './logic/getProxyFormState';\nimport deepEqual from './utils/deepEqual';\nimport isFunction from './utils/isFunction';\nimport { createFormControl } from './logic';\nimport type {\n  FieldValues,\n  FormState,\n  UseFormProps,\n  UseFormReturn,\n} from './types';\nimport { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect';\n\n/**\n * Custom hook to manage the entire form.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useform) • [Demo](https://codesandbox.io/s/react-hook-form-get-started-ts-5ksmm) • [Video](https://www.youtube.com/watch?v=RkXv4AXXC_4)\n *\n * @param props - form configuration and validation parameters.\n *\n * @returns methods - individual functions to manage the form state. {@link UseFormReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, watch, formState: { errors } } = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   console.log(watch(\"example\"));\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input defaultValue=\"test\" {...register(\"example\")} />\n *       <input {...register(\"exampleRequired\", { required: true })} />\n *       {errors.exampleRequired && <span>This field is required</span>}\n *       <button>Submit</button>\n *     </form>\n *   );\n * }\n * ```\n */\nexport function useForm<\n  TFieldValues extends FieldValues = FieldValues,\n  TContext = any,\n  TTransformedValues = TFieldValues,\n>(\n  props: UseFormProps<TFieldValues, TContext, TTransformedValues> = {},\n): UseFormReturn<TFieldValues, TContext, TTransformedValues> {\n  const _formControl = React.useRef<\n    UseFormReturn<TFieldValues, TContext, TTransformedValues> | undefined\n  >(undefined);\n  const _values = React.useRef<typeof props.values>(undefined);\n  const [formState, updateFormState] = React.useState<FormState<TFieldValues>>({\n    isDirty: false,\n    isValidating: false,\n    isLoading: isFunction(props.defaultValues),\n    isSubmitted: false,\n    isSubmitting: false,\n    isSubmitSuccessful: false,\n    isValid: false,\n    submitCount: 0,\n    dirtyFields: {},\n    touchedFields: {},\n    validatingFields: {},\n    errors: props.errors || {},\n    disabled: props.disabled || false,\n    isReady: false,\n    defaultValues: isFunction(props.defaultValues)\n      ? undefined\n      : props.defaultValues,\n  });\n\n  if (!_formControl.current) {\n    if (props.formControl) {\n      _formControl.current = {\n        ...props.formControl,\n        formState,\n      };\n\n      if (props.defaultValues && !isFunction(props.defaultValues)) {\n        props.formControl.reset(props.defaultValues, props.resetOptions);\n      }\n    } else {\n      const { formControl, ...rest } = createFormControl(props);\n\n      _formControl.current = {\n        ...rest,\n        formState,\n      };\n    }\n  }\n\n  const control = _formControl.current.control;\n  control._options = props;\n\n  useIsomorphicLayoutEffect(() => {\n    const sub = control._subscribe({\n      formState: control._proxyFormState,\n      callback: () => updateFormState({ ...control._formState }),\n      reRenderRoot: true,\n    });\n\n    updateFormState((data) => ({\n      ...data,\n      isReady: true,\n    }));\n\n    control._formState.isReady = true;\n\n    return sub;\n  }, [control]);\n\n  React.useEffect(\n    () => control._disableForm(props.disabled),\n    [control, props.disabled],\n  );\n\n  React.useEffect(() => {\n    if (props.mode) {\n      control._options.mode = props.mode;\n    }\n    if (props.reValidateMode) {\n      control._options.reValidateMode = props.reValidateMode;\n    }\n  }, [control, props.mode, props.reValidateMode]);\n\n  React.useEffect(() => {\n    if (props.errors) {\n      control._setErrors(props.errors);\n      control._focusError();\n    }\n  }, [control, props.errors]);\n\n  React.useEffect(() => {\n    props.shouldUnregister &&\n      control._subjects.state.next({\n        values: control._getWatch(),\n      });\n  }, [control, props.shouldUnregister]);\n\n  React.useEffect(() => {\n    if (control._proxyFormState.isDirty) {\n      const isDirty = control._getDirty();\n      if (isDirty !== formState.isDirty) {\n        control._subjects.state.next({\n          isDirty,\n        });\n      }\n    }\n  }, [control, formState.isDirty]);\n\n  React.useEffect(() => {\n    if (props.values && !deepEqual(props.values, _values.current)) {\n      control._reset(props.values, {\n        keepFieldsRef: true,\n        ...control._options.resetOptions,\n      });\n      _values.current = props.values;\n      updateFormState((state) => ({ ...state }));\n    } else {\n      control._resetDefaultValues();\n    }\n  }, [control, props.values]);\n\n  React.useEffect(() => {\n    if (!control._state.mount) {\n      control._setValid();\n      control._state.mount = true;\n    }\n\n    if (control._state.watch) {\n      control._state.watch = false;\n      control._subjects.state.next({ ...control._formState });\n    }\n\n    control._removeUnmounted();\n  });\n\n  _formControl.current.formState = getProxyFormState(formState, control);\n\n  return _formControl.current;\n}\n"], "names": ["isCheckBoxInput", "element", "type", "isDateObject", "value", "Date", "isNullOrUndefined", "isObjectType", "isObject", "Array", "isArray", "getEventValue", "event", "target", "checked", "isNameInFieldArray", "names", "name", "has", "substring", "search", "getNodeParentName", "isWeb", "window", "HTMLElement", "document", "cloneObject", "data", "copy", "isFileListInstance", "FileList", "Blob", "tempObject", "prototypeCopy", "constructor", "prototype", "hasOwnProperty", "isPlainObject", "key", "is<PERSON>ey", "test", "isUndefined", "val", "undefined", "compact", "filter", "Boolean", "stringToPath", "input", "replace", "split", "get", "object", "path", "defaultValue", "result", "reduce", "isBoolean", "set", "index", "temp<PERSON>ath", "length", "lastIndex", "newValue", "objValue", "isNaN", "EVENTS", "VALIDATION_MODE", "INPUT_VALIDATION_RULES", "HookFormContext", "React", "createContext", "displayName", "useFormContext", "useContext", "getProxyFormState", "formState", "control", "localProxyFormState", "isRoot", "defaultValues", "_defaultValues", "Object", "defineProperty", "_key", "_proxyFormState", "useIsomorphicLayoutEffect", "useLayoutEffect", "useEffect", "useFormState", "props", "methods", "disabled", "exact", "updateFormState", "useState", "_formState", "_localProxyFormState", "useRef", "isDirty", "isLoading", "dirtyFields", "touchedFields", "validatingFields", "isValidating", "<PERSON><PERSON><PERSON><PERSON>", "errors", "_subscribe", "current", "callback", "_setValid", "useMemo", "isString", "generateWatchOutput", "_names", "formValues", "isGlobal", "watch", "add", "map", "fieldName", "watchAll", "useWatch", "_defaultValue", "updateValue", "_getWatch", "values", "_formValues", "_removeUnmounted", "useController", "shouldUnregister", "isArrayField", "array", "_props", "_registerProps", "register", "rules", "fieldState", "defineProperties", "invalid", "enumerable", "isTouched", "error", "onChange", "useCallback", "onBlur", "ref", "elm", "field", "_fields", "_f", "focus", "select", "setCustomValidity", "message", "reportValidity", "_shouldUnregisterField", "_options", "updateMounted", "mount", "_state", "action", "unregister", "_setDisabledField", "flatten", "obj", "output", "keys", "nested", "nested<PERSON><PERSON>", "POST_REQUEST", "appendErrors", "validateAllFieldCriteria", "types", "convertToArrayPayload", "createSubject", "_observers", "observers", "next", "observer", "subscribe", "push", "unsubscribe", "o", "isPrimitive", "deepEqual", "object1", "object2", "_internal_visited", "WeakSet", "getTime", "keys1", "keys2", "val1", "includes", "val2", "isEmptyObject", "isFileInput", "isFunction", "isHTMLElement", "owner", "ownerDocument", "defaultView", "isMultipleSelect", "isRadioInput", "live", "isConnected", "unset", "paths", "childObject", "updatePath", "slice", "baseGet", "isEmptyArray", "objectHasFunction", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fields", "isParentNodeArray", "getDirtyFieldsFromDefaultValues", "dirtyField<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getDirty<PERSON>ields", "defaultResult", "validResult", "getCheckboxValue", "options", "option", "attributes", "getFieldValueAs", "valueAsNumber", "valueAsDate", "setValueAs", "NaN", "defaultReturn", "getRadioValue", "previous", "getFieldValue", "files", "refs", "selectedOptions", "isCheckBox", "isRegex", "RegExp", "getRuleValue", "rule", "source", "getValidationModes", "mode", "isOnSubmit", "isOnBlur", "isOnChange", "isOnAll", "isOnTouch", "ASYNC_FUNCTION", "hasPromiseValidation", "fieldReference", "validate", "find", "validateFunction", "isWatched", "isBlurEvent", "some", "watchName", "startsWith", "iterateFieldsByAction", "fieldsNames", "abort<PERSON><PERSON><PERSON>", "current<PERSON><PERSON>", "schemaErrorLookup", "join", "found<PERSON><PERSON>r", "root", "pop", "updateFieldArrayRootError", "fieldArrayErrors", "isMessage", "getValidateError", "every", "getValueAndMessage", "validationData", "validateField", "async", "disabled<PERSON>ieldN<PERSON>s", "shouldUseNativeValidation", "isFieldArray", "required", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "min", "max", "pattern", "inputValue", "inputRef", "isRadio", "isRadioOrCheckbox", "isEmpty", "appendErrors<PERSON><PERSON><PERSON>", "bind", "getMinMaxMessage", "exceedMax", "maxLengthMessage", "minLengthMessage", "maxType", "minType", "exceedMin", "maxOutput", "minOutput", "valueDate", "convertTimeToDate", "time", "toDateString", "isTime", "isWeek", "valueNumber", "maxLengthOutput", "minLengthOutput", "patternValue", "match", "validateError", "validationResult", "defaultOptions", "reValidateMode", "shouldFocusError", "createFormControl", "delayError<PERSON><PERSON><PERSON>", "submitCount", "isReady", "isSubmitted", "isSubmitting", "isSubmitSuccessful", "Set", "unMount", "timer", "_proxySubscribeFormState", "_subjects", "state", "shouldDisplayAllAssociatedErrors", "criteriaMode", "shouldUpdateValid", "resolver", "_runSchema", "executeBuiltInValidation", "_updateIsValidating", "from", "for<PERSON>ach", "updateValidAndValue", "shouldSkipSetValueAs", "defaultChecked", "setFieldValue", "updateTouchAndDirty", "fieldValue", "should<PERSON>irty", "shouldRender", "shouldUpdateField", "is<PERSON>revious<PERSON><PERSON>y", "_getDirty", "isCurrentFieldPristine", "isPreviousFieldTouched", "shouldRenderByError", "previousFieldError", "delayError", "updateErrors", "wait", "clearTimeout", "setTimeout", "updatedFormState", "context", "getResolverOptions", "should<PERSON>nly<PERSON><PERSON><PERSON><PERSON>d", "valid", "isFieldArrayRoot", "isPromiseFunction", "fieldError", "getV<PERSON>ues", "optionRef", "selected", "checkboxRef", "radioRef", "shouldTouch", "shouldValidate", "trigger", "set<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "setValue", "cloneValue", "isFieldValueUpdated", "_updateIsFieldValueUpdated", "Number", "validationModeBeforeSubmit", "validationModeAfterSubmit", "shouldSkipValidation", "deps", "skipValidation", "watched", "previousErrorLookupResult", "errorLookupResult", "_focusInput", "fieldNames", "executeSchemaAndUpdateState", "Promise", "all", "shouldFocus", "getFieldState", "setError", "currentError", "currentRef", "restOfErrorTree", "signalName", "currentName", "formStateData", "shouldRenderFormState", "_setFormState", "reRenderRoot", "delete", "keepValue", "keepError", "keep<PERSON>irty", "keepTouched", "keepIsValidating", "keepDefaultValue", "keepIsValid", "disabledIsDefined", "progressive", "fieldRef", "querySelectorAll", "radioOrCheckbox", "_focusError", "handleSubmit", "onValid", "onInvalid", "e", "onValidError", "preventDefault", "persist", "field<PERSON><PERSON><PERSON>", "size", "_reset", "keepStateOptions", "updatedValues", "cloneUpdatedValues", "isEmptyResetValues", "keepDefaultValues", "keepV<PERSON>ues", "keepDirtyV<PERSON>ues", "fieldsToCheck", "form", "closest", "reset", "keepFieldsRef", "keepSubmitCount", "keepIsSubmitted", "keepErrors", "keepIsSubmitSuccessful", "_setFieldArray", "method", "args", "shouldSetValues", "shouldUpdateFieldsAndState", "argA", "argB", "unsetEmptyArray", "_setErrors", "_getFieldArray", "_resetDefaultValues", "then", "resetOptions", "_disableForm", "payload", "reset<PERSON>ield", "clearErrors", "inputName", "setFocus", "shouldSelect", "formControl", "generateId", "crypto", "randomUUID", "d", "performance", "now", "c", "r", "Math", "random", "toString", "getFocusFieldName", "focusName", "focusIndex", "appendAt", "fillEmptyArray", "insert", "moveArrayAt", "to", "splice", "prependAt", "removeArrayAt", "indexes", "i", "temp", "removeAtIndexes", "sort", "a", "b", "swapArrayAt", "indexA", "indexB", "updateAt", "render", "mounted", "setMounted", "onSubmit", "children", "headers", "encType", "onError", "onSuccess", "validateStatus", "rest", "submit", "<PERSON><PERSON><PERSON><PERSON>", "formData", "FormData", "formDataJson", "JSON", "stringify", "_a", "flattenForm<PERSON><PERSON>ues", "append", "shouldStringifySubmissionData", "response", "fetch", "String", "body", "status", "createElement", "Fragment", "noValidate", "Provider", "keyName", "setFields", "ids", "_fieldIds", "_name", "_actioned", "fieldArrayName", "updateValues", "updatedFieldArrayValues", "existingError", "swap", "move", "prepend", "prependValue", "appendValue", "remove", "insertValue", "insertAt", "update", "item", "_formControl", "_values", "sub"], "mappings": "miBAEAA,EAAgBC,GACG,aAAjBA,EAAQC,KCHVC,EAAgBC,GAAkCA,aAAiBC,KCAnEC,EAAgBF,GAAuD,MAATA,ECGvD,MAAMG,EAAgBH,GACV,iBAAVA,EAET,IAAAI,EAAkCJ,IAC/BE,EAAkBF,KAClBK,MAAMC,QAAQN,IACfG,EAAaH,KACZD,EAAaC,GCLhBO,EAAgBC,GACdJ,EAASI,IAAWA,EAAgBC,OAChCb,EAAiBY,EAAgBC,QAC9BD,EAAgBC,OAAOC,QACvBF,EAAgBC,OAAOT,MAC1BQ,ECNNG,EAAe,CAACC,EAA+BC,IAC7CD,EAAME,ICLO,CAACD,GACdA,EAAKE,UAAU,EAAGF,EAAKG,OAAO,iBAAmBH,EDIvCI,CAAkBJ,IEL9BK,EAAiC,oBAAXC,aACU,IAAvBA,OAAOC,aACM,oBAAbC,SCEK,SAAUC,EAAeC,GACrC,IAAIC,EACJ,MAAMlB,EAAUD,MAAMC,QAAQiB,GACxBE,EACgB,oBAAbC,UAA2BH,aAAgBG,SAEpD,GAAIH,aAAgBtB,KAClBuB,EAAO,IAAIvB,KAAKsB,OACX,IACHL,IAAUK,aAAgBI,MAAQF,KACnCnB,IAAWF,EAASmB,GAcrB,OAAOA,EAVP,GAFAC,EAAOlB,EAAU,GAAK,CAAA,EAEjBA,GChBM,CAACsB,IACd,MAAMC,EACJD,EAAWE,aAAeF,EAAWE,YAAYC,UAEnD,OACE3B,EAASyB,IAAkBA,EAAcG,eAAe,kBDWvCC,CAAcV,GAG7B,IAAK,MAAMW,KAAOX,EACZA,EAAKS,eAAeE,KACtBV,EAAKU,GAAOZ,EAAYC,EAAKW,UAJjCV,EAAOD,EAYX,OAAOC,CACT,CEhCA,IAAAW,EAAgBnC,GAAkB,QAAQoC,KAAKpC,GCA/CqC,EAAgBC,QAA2CC,IAARD,ECAnDE,EAAwBxC,GACtBK,MAAMC,QAAQN,GAASA,EAAMyC,OAAOC,SAAW,GCCjDC,EAAgBC,GACdJ,EAAQI,EAAMC,QAAQ,YAAa,IAAIC,MAAM,UCG/CC,EAAe,CACbC,EACAC,EACAC,KAEA,IAAKD,IAAS7C,EAAS4C,GACrB,OAAOE,EAGT,MAAMC,GAAUhB,EAAMc,GAAQ,CAACA,GAAQN,EAAaM,IAAOG,OACzD,CAACD,EAAQjB,IACPhC,EAAkBiD,GAAUA,EAASA,EAAOjB,GAC9Cc,GAGF,OAAOX,EAAYc,IAAWA,IAAWH,EACrCX,EAAYW,EAAOC,IACjBC,EACAF,EAAOC,GACTE,GCzBNE,EAAgBrD,GAAsD,kBAAVA,ECM5DsD,EAAe,CACbN,EACAC,EACAjD,KAEA,IAAIuD,GAAQ,EACZ,MAAMC,EAAWrB,EAAMc,GAAQ,CAACA,GAAQN,EAAaM,GAC/CQ,EAASD,EAASC,OAClBC,EAAYD,EAAS,EAE3B,OAASF,EAAQE,GAAQ,CACvB,MAAMvB,EAAMsB,EAASD,GACrB,IAAII,EAAW3D,EAEf,GAAIuD,IAAUG,EAAW,CACvB,MAAME,EAAWZ,EAAOd,GACxByB,EACEvD,EAASwD,IAAavD,MAAMC,QAAQsD,GAChCA,EACCC,OAAOL,EAASD,EAAQ,IAEvB,CAAA,EADA,GAIV,GAAY,cAARrB,GAA+B,gBAARA,GAAiC,cAARA,EAClD,OAGFc,EAAOd,GAAOyB,EACdX,EAASA,EAAOd,KCnCb,MAAM4B,EACL,OADKA,EAEA,WAFAA,EAGH,SAGGC,EACH,SADGA,EAED,WAFCA,EAGD,WAHCA,EAIA,YAJAA,EAKN,MAGMC,EACN,MADMA,EAEN,MAFMA,EAGA,YAHAA,EAIA,YAJAA,EAKF,UALEA,EAMD,WANCA,EAOD,WCjBNC,EAAkBC,EAAMC,cAAoC,MAClEF,EAAgBG,YAAc,kBAgCvB,MAAMC,EAAiB,IAK5BH,EAAMI,WAAWL,GCvCnB,IAAAM,EAAe,CAKbC,EACAC,EACAC,EACAC,GAAS,KAET,MAAMxB,EAAS,CACbyB,cAAeH,EAAQI,gBAGzB,IAAK,MAAM3C,KAAOsC,EAChBM,OAAOC,eAAe5B,EAAQjB,EAAK,CACjCa,IAAK,KACH,MAAMiC,EAAO9C,EAOb,OALIuC,EAAQQ,gBAAgBD,KAAUjB,IACpCU,EAAQQ,gBAAgBD,IAASL,GAAUZ,GAG7CW,IAAwBA,EAAoBM,IAAQ,GAC7CR,EAAUQ,MAKvB,OAAO7B,GC9BF,MAAM+B,EACO,oBAAX/D,OAAyB+C,EAAMiB,gBAAkBjB,EAAMkB,UCsC1D,SAAUC,EAIdC,GAEA,MAAMC,EAAUlB,KACVI,QAAEA,EAAUc,EAAQd,QAAOe,SAAEA,EAAQ3E,KAAEA,EAAI4E,MAAEA,GAAUH,GAAS,CAAA,GAC/Dd,EAAWkB,GAAmBxB,EAAMyB,SAASlB,EAAQmB,YACtDC,EAAuB3B,EAAM4B,OAAO,CACxCC,SAAS,EACTC,WAAW,EACXC,aAAa,EACbC,eAAe,EACfC,kBAAkB,EAClBC,cAAc,EACdC,SAAS,EACTC,QAAQ,IAwBV,OArBApB,EACE,IACET,EAAQ8B,WAAW,CACjB1F,OACA2D,UAAWqB,EAAqBW,QAChCf,QACAgB,SAAWjC,KACRgB,GACCE,EAAgB,IACXjB,EAAQmB,cACRpB,OAIb,CAAC3D,EAAM2E,EAAUC,IAGnBvB,EAAMkB,UAAU,KACdS,EAAqBW,QAAQH,SAAW5B,EAAQiC,WAAU,IACzD,CAACjC,IAEGP,EAAMyC,QACX,IACEpC,EACEC,EACAC,EACAoB,EAAqBW,SACrB,GAEJ,CAAChC,EAAWC,GAEhB,CC5FA,IAAAmC,EAAgB5G,GAAqD,iBAAVA,ECI3D6G,EAAe,CACbjG,EACAkG,EACAC,EACAC,EACA9D,IAEI0D,EAAShG,IACXoG,GAAYF,EAAOG,MAAMC,IAAItG,GACtBmC,EAAIgE,EAAYnG,EAAOsC,IAG5B7C,MAAMC,QAAQM,GACTA,EAAMuG,IACVC,IACCJ,GAAYF,EAAOG,MAAMC,IAAIE,GAC7BrE,EAAIgE,EAAYK,MAKtBJ,IAAaF,EAAOO,UAAW,GAExBN,GCqHH,SAAUO,EACdhC,GAEA,MAAMC,EAAUlB,KACVI,QACJA,EAAUc,EAAQd,QAAO5D,KACzBA,EAAIqC,aACJA,EAAYsC,SACZA,EAAQC,MACRA,GACEH,GAAS,CAAA,EACPiC,EAAgBrD,EAAM4B,OAAO5C,IAC5BlD,EAAOwH,GAAetD,EAAMyB,SACjClB,EAAQgD,UACN5G,EACA0G,EAAcf,UA6BlB,OAzBAtB,EACE,IACET,EAAQ8B,WAAW,CACjB1F,OACA2D,UAAW,CACTkD,QAAQ,GAEVjC,QACAgB,SAAWjC,IACRgB,GACDgC,EACEX,EACEhG,EACA4D,EAAQqC,OACRtC,EAAUkD,QAAUjD,EAAQkD,aAC5B,EACAJ,EAAcf,YAIxB,CAAC3F,EAAM4D,EAASe,EAAUC,IAG5BvB,EAAMkB,UAAU,IAAMX,EAAQmD,oBAEvB5H,CACT,CC7IM,SAAU6H,EAKdvC,GAEA,MAAMC,EAAUlB,KACVxD,KAAEA,EAAI2E,SAAEA,EAAQf,QAAEA,EAAUc,EAAQd,QAAOqD,iBAAEA,GAAqBxC,EAClEyC,EAAepH,EAAmB8D,EAAQqC,OAAOkB,MAAOnH,GACxDb,EAAQsH,EAAS,CACrB7C,UACA5D,OACAqC,aAAcH,EACZ0B,EAAQkD,YACR9G,EACAkC,EAAI0B,EAAQI,eAAgBhE,EAAMyE,EAAMpC,eAE1CuC,OAAO,IAEHjB,EAAYa,EAAa,CAC7BZ,UACA5D,OACA4E,OAAO,IAGHwC,EAAS/D,EAAM4B,OAAOR,GACtB4C,EAAiBhE,EAAM4B,OAC3BrB,EAAQ0D,SAAStH,EAAM,IAClByE,EAAM8C,MACTpI,WACIqD,EAAUiC,EAAME,UAAY,CAAEA,SAAUF,EAAME,UAAa,MAI7D6C,EAAanE,EAAMyC,QACvB,IACE7B,OAAOwD,iBACL,GACA,CACEC,QAAS,CACPC,YAAY,EACZzF,IAAK,MAAQA,EAAIyB,EAAU8B,OAAQzF,IAErCkF,QAAS,CACPyC,YAAY,EACZzF,IAAK,MAAQA,EAAIyB,EAAUyB,YAAapF,IAE1C4H,UAAW,CACTD,YAAY,EACZzF,IAAK,MAAQA,EAAIyB,EAAU0B,cAAerF,IAE5CuF,aAAc,CACZoC,YAAY,EACZzF,IAAK,MAAQA,EAAIyB,EAAU2B,iBAAkBtF,IAE/C6H,MAAO,CACLF,YAAY,EACZzF,IAAK,IAAMA,EAAIyB,EAAU8B,OAAQzF,MAIzC,CAAC2D,EAAW3D,IAGR8H,EAAWzE,EAAM0E,YACpBpI,GACC0H,EAAe1B,QAAQmC,SAAS,CAC9BlI,OAAQ,CACNT,MAAOO,EAAcC,GACrBK,KAAMA,GAERf,KAAMgE,IAEV,CAACjD,IAGGgI,EAAS3E,EAAM0E,YACnB,IACEV,EAAe1B,QAAQqC,OAAO,CAC5BpI,OAAQ,CACNT,MAAO+C,EAAI0B,EAAQkD,YAAa9G,GAChCA,KAAMA,GAERf,KAAMgE,IAEV,CAACjD,EAAM4D,EAAQkD,cAGXmB,EAAM5E,EAAM0E,YACfG,IACC,MAAMC,EAAQjG,EAAI0B,EAAQwE,QAASpI,GAE/BmI,GAASD,IACXC,EAAME,GAAGJ,IAAM,CACbK,MAAO,IAAMJ,EAAII,OAASJ,EAAII,QAC9BC,OAAQ,IAAML,EAAIK,QAAUL,EAAIK,SAChCC,kBAAoBC,GAClBP,EAAIM,kBAAkBC,GACxBC,eAAgB,IAAMR,EAAIQ,oBAIhC,CAAC9E,EAAQwE,QAASpI,IAGdmI,EAAQ9E,EAAMyC,QAClB,KAAA,CACE9F,OACAb,WACIqD,EAAUmC,IAAahB,EAAUgB,SACjC,CAAEA,SAAUhB,EAAUgB,UAAYA,GAClC,GACJmD,WACAE,SACAC,QAEF,CAACjI,EAAM2E,EAAUhB,EAAUgB,SAAUmD,EAAUE,EAAQC,EAAK9I,IAoD9D,OAjDAkE,EAAMkB,UAAU,KACd,MAAMoE,EACJ/E,EAAQgF,SAAS3B,kBAAoBA,EAEvCrD,EAAQ0D,SAAStH,EAAM,IAClBoH,EAAOzB,QAAQ4B,SACd/E,EAAU4E,EAAOzB,QAAQhB,UACzB,CAAEA,SAAUyC,EAAOzB,QAAQhB,UAC3B,KAGN,MAAMkE,EAAgB,CAAC7I,EAAyBb,KAC9C,MAAMgJ,EAAejG,EAAI0B,EAAQwE,QAASpI,GAEtCmI,GAASA,EAAME,KACjBF,EAAME,GAAGS,MAAQ3J,IAMrB,GAFA0J,EAAc7I,GAAM,GAEhB2I,EAAwB,CAC1B,MAAMxJ,EAAQsB,EAAYyB,EAAI0B,EAAQgF,SAAS7E,cAAe/D,IAC9DyC,EAAImB,EAAQI,eAAgBhE,EAAMb,GAC9BqC,EAAYU,EAAI0B,EAAQkD,YAAa9G,KACvCyC,EAAImB,EAAQkD,YAAa9G,EAAMb,GAMnC,OAFC+H,GAAgBtD,EAAQ0D,SAAStH,GAE3B,MAEHkH,EACIyB,IAA2B/E,EAAQmF,OAAOC,OAC1CL,GAEF/E,EAAQqF,WAAWjJ,GACnB6I,EAAc7I,GAAM,KAEzB,CAACA,EAAM4D,EAASsD,EAAcD,IAEjC5D,EAAMkB,UAAU,KACdX,EAAQsF,kBAAkB,CACxBvE,WACA3E,UAED,CAAC2E,EAAU3E,EAAM4D,IAEbP,EAAMyC,QACX,KAAA,CACEqC,QACAxE,YACA6D,eAEF,CAACW,EAAOxE,EAAW6D,GAEvB,CCpLA,MCzCa2B,EAAWC,IACtB,MAAMC,EAAsB,CAAA,EAE5B,IAAK,MAAMhI,KAAO4C,OAAOqF,KAAKF,GAC5B,GAAI9J,EAAa8J,EAAI/H,KAAsB,OAAb+H,EAAI/H,GAAe,CAC/C,MAAMkI,EAASJ,EAAQC,EAAI/H,IAE3B,IAAK,MAAMmI,KAAavF,OAAOqF,KAAKC,GAClCF,EAAO,GAAGhI,KAAOmI,KAAeD,EAAOC,QAGzCH,EAAOhI,GAAO+H,EAAI/H,GAItB,OAAOgI,GCbHI,EAAe,OCArB,IAAAC,EAAe,CACb1J,EACA2J,EACAlE,EACAxG,EACAwJ,IAEAkB,EACI,IACKlE,EAAOzF,GACV4J,MAAO,IACDnE,EAAOzF,IAASyF,EAAOzF,GAAO4J,MAAQnE,EAAOzF,GAAO4J,MAAQ,CAAA,EAChE3K,CAACA,GAAOwJ,IAAW,IAGvB,CAAA,ECrBNoB,EAAmB1K,GAAcK,MAAMC,QAAQN,GAASA,EAAQ,CAACA,GCgBjE2K,EAAe,KACb,IAAIC,EAA4B,GAqBhC,MAAO,CACL,aAAIC,GACF,OAAOD,GAETE,KAvBY9K,IACZ,IAAK,MAAM+K,KAAYH,EACrBG,EAASD,MAAQC,EAASD,KAAK9K,IAsBjCgL,UAlBiBD,IACjBH,EAAWK,KAAKF,GACT,CACLG,YAAa,KACXN,EAAaA,EAAWnI,OAAQ0I,GAAMA,IAAMJ,MAehDG,YAVkB,KAClBN,EAAa,MC9BjBQ,EAAgBpL,GACdE,EAAkBF,KAAWG,EAAaH,GCD9B,SAAUqL,EACtBC,EACAC,EACAC,EAAoB,IAAIC,SAExB,GAAIL,EAAYE,IAAYF,EAAYG,GACtC,OAAOD,IAAYC,EAGrB,GAAIxL,EAAauL,IAAYvL,EAAawL,GACxC,OAAOD,EAAQI,YAAcH,EAAQG,UAGvC,MAAMC,EAAQ7G,OAAOqF,KAAKmB,GACpBM,EAAQ9G,OAAOqF,KAAKoB,GAE1B,GAAII,EAAMlI,SAAWmI,EAAMnI,OACzB,OAAO,EAGT,GAAI+H,EAAkB1K,IAAIwK,IAAYE,EAAkB1K,IAAIyK,GAC1D,OAAO,EAETC,EAAkBtE,IAAIoE,GACtBE,EAAkBtE,IAAIqE,GAEtB,IAAK,MAAMrJ,KAAOyJ,EAAO,CACvB,MAAME,EAAOP,EAAQpJ,GAErB,IAAK0J,EAAME,SAAS5J,GAClB,OAAO,EAGT,GAAY,QAARA,EAAe,CACjB,MAAM6J,EAAOR,EAAQrJ,GAErB,GACGnC,EAAa8L,IAAS9L,EAAagM,IACnC3L,EAASyL,IAASzL,EAAS2L,IAC3B1L,MAAMC,QAAQuL,IAASxL,MAAMC,QAAQyL,IACjCV,EAAUQ,EAAME,EAAMP,GACvBK,IAASE,EAEb,OAAO,GAKb,OAAO,CACT,CClDA,IAAAC,EAAgBhM,GACdI,EAASJ,KAAW8E,OAAOqF,KAAKnK,GAAOyD,OCHzCwI,EAAgBpM,GACG,SAAjBA,EAAQC,KCHVoM,EAAgBlM,GACG,mBAAVA,ECCTmM,GAAgBnM,IACd,IAAKkB,EACH,OAAO,EAGT,MAAMkL,EAAQpM,EAAUA,EAAsBqM,cAA6B,EAC3E,OACErM,aACCoM,GAASA,EAAME,YAAcF,EAAME,YAAYlL,YAAcA,cCRlEmL,GAAgB1M,GACG,oBAAjBA,EAAQC,KCDV0M,GAAgB3M,GACG,UAAjBA,EAAQC,KCCV2M,GAAgB3D,GAAaqD,GAAcrD,IAAQA,EAAI4D,YCsBzC,SAAUC,GAAM3J,EAAaC,GACzC,MAAM2J,EAAQvM,MAAMC,QAAQ2C,GACxBA,EACAd,EAAMc,GACJ,CAACA,GACDN,EAAaM,GAEb4J,EAA+B,IAAjBD,EAAMnJ,OAAeT,EA3B3C,SAAiBA,EAAa8J,GAC5B,MAAMrJ,EAASqJ,EAAWC,MAAM,GAAG,GAAItJ,OACvC,IAAIF,EAAQ,EAEZ,KAAOA,EAAQE,GACbT,EAASX,EAAYW,GAAUO,IAAUP,EAAO8J,EAAWvJ,MAG7D,OAAOP,CACT,CAkBoDgK,CAAQhK,EAAQ4J,GAE5DrJ,EAAQqJ,EAAMnJ,OAAS,EACvBvB,EAAM0K,EAAMrJ,GAclB,OAZIsJ,UACKA,EAAY3K,GAIT,IAAVqB,IACEnD,EAASyM,IAAgBb,EAAca,IACtCxM,MAAMC,QAAQuM,IA5BrB,SAAsB5C,GACpB,IAAK,MAAM/H,KAAO+H,EAChB,GAAIA,EAAIjI,eAAeE,KAASG,EAAY4H,EAAI/H,IAC9C,OAAO,EAGX,OAAO,CACT,CAqBqC+K,CAAaJ,KAE9CF,GAAM3J,EAAQ4J,EAAMG,MAAM,GAAG,IAGxB/J,CACT,CCjDA,IAAAkK,GAAmB3L,IACjB,IAAK,MAAMW,KAAOX,EAChB,GAAI2K,EAAW3K,EAAKW,IAClB,OAAO,EAGX,OAAO,GCDT,SAASiL,GAAmB5L,EAAS6L,EAA8B,IACjE,MAAMC,EAAoBhN,MAAMC,QAAQiB,GAExC,GAAInB,EAASmB,IAAS8L,EACpB,IAAK,MAAMnL,KAAOX,EAEdlB,MAAMC,QAAQiB,EAAKW,KAClB9B,EAASmB,EAAKW,MAAUgL,GAAkB3L,EAAKW,KAEhDkL,EAAOlL,GAAO7B,MAAMC,QAAQiB,EAAKW,IAAQ,GAAK,CAAA,EAC9CiL,GAAgB5L,EAAKW,GAAMkL,EAAOlL,KACxBhC,EAAkBqB,EAAKW,MACjCkL,EAAOlL,IAAO,GAKpB,OAAOkL,CACT,CAEA,SAASE,GACP/L,EACAwF,EACAwG,GAKA,MAAMF,EAAoBhN,MAAMC,QAAQiB,GAExC,GAAInB,EAASmB,IAAS8L,EACpB,IAAK,MAAMnL,KAAOX,EAEdlB,MAAMC,QAAQiB,EAAKW,KAClB9B,EAASmB,EAAKW,MAAUgL,GAAkB3L,EAAKW,IAG9CG,EAAY0E,IACZqE,EAAYmC,EAAsBrL,IAElCqL,EAAsBrL,GAAO7B,MAAMC,QAAQiB,EAAKW,IAC5CiL,GAAgB5L,EAAKW,GAAM,IAC3B,IAAKiL,GAAgB5L,EAAKW,KAE9BoL,GACE/L,EAAKW,GACLhC,EAAkB6G,GAAc,CAAA,EAAKA,EAAW7E,GAChDqL,EAAsBrL,IAI1BqL,EAAsBrL,IAAQmJ,EAAU9J,EAAKW,GAAM6E,EAAW7E,IAKpE,OAAOqL,CACT,CAEA,IAAAC,GAAe,CAAI5I,EAAkBmC,IACnCuG,GACE1I,EACAmC,EACAoG,GAAgBpG,IC/DpB,MAAM0G,GAAqC,CACzCzN,OAAO,EACPqG,SAAS,GAGLqH,GAAc,CAAE1N,OAAO,EAAMqG,SAAS,GAE5C,IAAAsH,GAAgBC,IACd,GAAIvN,MAAMC,QAAQsN,GAAU,CAC1B,GAAIA,EAAQnK,OAAS,EAAG,CACtB,MAAMiE,EAASkG,EACZnL,OAAQoL,GAAWA,GAAUA,EAAOnN,UAAYmN,EAAOrI,UACvD2B,IAAK0G,GAAWA,EAAO7N,OAC1B,MAAO,CAAEA,MAAO0H,EAAQrB,UAAWqB,EAAOjE,QAG5C,OAAOmK,EAAQ,GAAGlN,UAAYkN,EAAQ,GAAGpI,SAErCoI,EAAQ,GAAGE,aAAezL,EAAYuL,EAAQ,GAAGE,WAAW9N,OAC1DqC,EAAYuL,EAAQ,GAAG5N,QAA+B,KAArB4N,EAAQ,GAAG5N,MAC1C0N,GACA,CAAE1N,MAAO4N,EAAQ,GAAG5N,MAAOqG,SAAS,GACtCqH,GACFD,GAGN,OAAOA,IC7BTM,GAAe,CACb/N,GACEgO,gBAAeC,cAAaC,gBAE9B7L,EAAYrC,GACRA,EACAgO,EACY,KAAVhO,EACEmO,IACAnO,GACGA,EACDA,EACJiO,GAAerH,EAAS5G,GACtB,IAAIC,KAAKD,GACTkO,EACEA,EAAWlO,GACXA,ECfZ,MAAMoO,GAAkC,CACtC/H,SAAS,EACTrG,MAAO,MAGT,IAAAqO,GAAgBT,GACdvN,MAAMC,QAAQsN,GACVA,EAAQxK,OACN,CAACkL,EAAUT,IACTA,GAAUA,EAAOnN,UAAYmN,EAAOrI,SAChC,CACEa,SAAS,EACTrG,MAAO6N,EAAO7N,OAEhBsO,EACNF,IAEFA,GCXQ,SAAUG,GAAcrF,GACpC,MAAMJ,EAAMI,EAAGJ,IAEf,OAAImD,EAAYnD,GACPA,EAAI0F,MAGThC,GAAa1D,GACRuF,GAAcnF,EAAGuF,MAAMzO,MAG5BuM,GAAiBzD,GACZ,IAAIA,EAAI4F,iBAAiBvH,IAAI,EAAGnH,WAAYA,GAGjD2O,EAAW7F,GACN6E,GAAiBzE,EAAGuF,MAAMzO,MAG5B+N,GAAgB1L,EAAYyG,EAAI9I,OAASkJ,EAAGJ,IAAI9I,MAAQ8I,EAAI9I,MAAOkJ,EAC5E,CCpBA,ICXA0F,GAAgB5O,GAAoCA,aAAiB6O,OCSrEC,GACEC,GAEA1M,EAAY0M,GACRA,EACAH,GAAQG,GACNA,EAAKC,OACL5O,EAAS2O,GACPH,GAAQG,EAAK/O,OACX+O,EAAK/O,MAAMgP,OACXD,EAAK/O,MACP+O,ECjBVE,GAAgBC,IAAW,CACzBC,YAAaD,GAAQA,IAASnL,EAC9BqL,SAAUF,IAASnL,EACnBsL,WAAYH,IAASnL,EACrBuL,QAASJ,IAASnL,EAClBwL,UAAWL,IAASnL,ICJtB,MAAMyL,GAAiB,gBAEvB,IAAAC,GAAgBC,KACZA,KACAA,EAAeC,aAEdzD,EAAWwD,EAAeC,WACzBD,EAAeC,SAAS7N,YAAYjB,OAAS2O,IAC9CpP,EAASsP,EAAeC,WACvB7K,OAAO4C,OAAOgI,EAAeC,UAAUC,KACpCC,GACCA,EAAiB/N,YAAYjB,OAAS2O,KCbhDM,GAAe,CACbjP,EACAiG,EACAiJ,KAECA,IACAjJ,EAAOO,UACNP,EAAOG,MAAMnG,IAAID,IACjB,IAAIiG,EAAOG,OAAO+I,KACfC,GACCpP,EAAKqP,WAAWD,IAChB,SAAS7N,KAAKvB,EAAKkM,MAAMkD,EAAUxM,WCT3C,MAAM0M,GAAwB,CAC5B/C,EACAvD,EACAuG,EACAC,KAEA,IAAK,MAAMnO,KAAOkO,GAAetL,OAAOqF,KAAKiD,GAAS,CACpD,MAAMpE,EAAQjG,EAAIqK,EAAQlL,GAE1B,GAAI8G,EAAO,CACT,MAAME,GAAEA,KAAOoH,GAAiBtH,EAEhC,GAAIE,EAAI,CACN,GAAIA,EAAGuF,MAAQvF,EAAGuF,KAAK,IAAM5E,EAAOX,EAAGuF,KAAK,GAAIvM,KAASmO,EACvD,OAAO,EACF,GAAInH,EAAGJ,KAAOe,EAAOX,EAAGJ,IAAKI,EAAGrI,QAAUwP,EAC/C,OAAO,EAEP,GAAIF,GAAsBG,EAAczG,GACtC,WAGC,GAAIzJ,EAASkQ,IACdH,GAAsBG,EAA2BzG,GACnD,SCxBI,SAAU0G,GACtBjK,EACA2C,EACApI,GAKA,MAAM6H,EAAQ3F,EAAIuD,EAAQzF,GAE1B,GAAI6H,GAASvG,EAAMtB,GACjB,MAAO,CACL6H,QACA7H,QAIJ,MAAMD,EAAQC,EAAKiC,MAAM,KAEzB,KAAOlC,EAAM6C,QAAQ,CACnB,MAAM2D,EAAYxG,EAAM4P,KAAK,KACvBxH,EAAQjG,EAAIkG,EAAS7B,GACrBqJ,EAAa1N,EAAIuD,EAAQc,GAE/B,GAAI4B,IAAU3I,MAAMC,QAAQ0I,IAAUnI,IAASuG,EAC7C,MAAO,CAAEvG,QAGX,GAAI4P,GAAcA,EAAW3Q,KAC3B,MAAO,CACLe,KAAMuG,EACNsB,MAAO+H,GAIX,GAAIA,GAAcA,EAAWC,MAAQD,EAAWC,KAAK5Q,KACnD,MAAO,CACLe,KAAM,GAAGuG,SACTsB,MAAO+H,EAAWC,MAItB9P,EAAM+P,MAGR,MAAO,CACL9P,OAEJ,CC3CA,ICCA+P,GAAe,CACbtK,EACAoC,EACA7H,KAEA,MAAMgQ,EAAmBnG,EAAsB3H,EAAIuD,EAAQzF,IAG3D,OAFAyC,EAAIuN,EAAkB,OAAQnI,EAAM7H,IACpCyC,EAAIgD,EAAQzF,EAAMgQ,GACXvK,GCfTwK,GAAgB9Q,GAAqC4G,EAAS5G,GCChD,SAAU+Q,GACtB5N,EACA2F,EACAhJ,EAAO,YAEP,GACEgR,GAAU3N,IACT9C,MAAMC,QAAQ6C,IAAWA,EAAO6N,MAAMF,KACtCzN,EAAUF,KAAYA,EAEvB,MAAO,CACLrD,OACAwJ,QAASwH,GAAU3N,GAAUA,EAAS,GACtC2F,MAGN,CChBA,IAAAmI,GAAgBC,GACd9Q,EAAS8Q,KAAoBtC,GAAQsC,GACjCA,EACA,CACElR,MAAOkR,EACP5H,QAAS,ICwBjB6H,GAAeC,MACbpI,EACAqI,EACAtK,EACAyD,EACA8G,EACAC,KAEA,MAAMzI,IACJA,EAAG2F,KACHA,EAAI+C,SACJA,EAAQC,UACRA,EAASC,UACTA,EAASC,IACTA,EAAGC,IACHA,EAAGC,QACHA,EAAOlC,SACPA,EAAQ9O,KACRA,EAAImN,cACJA,EAAarE,MACbA,GACEX,EAAME,GACJ4I,EAA+B/O,EAAIgE,EAAYlG,GACrD,IAAK8I,GAAS0H,EAAmBvQ,IAAID,GACnC,MAAO,CAAA,EAET,MAAMkR,EAA6BtD,EAAOA,EAAK,GAAM3F,EAC/CO,EAAqBC,IACrBgI,GAA6BS,EAASxI,iBACxCwI,EAAS1I,kBAAkBhG,EAAUiG,GAAW,GAAKA,GAAW,IAChEyI,EAASxI,mBAGPb,EAA6B,CAAA,EAC7BsJ,EAAUxF,GAAa1D,GACvB6F,EAAa/O,EAAgBkJ,GAC7BmJ,EAAoBD,GAAWrD,EAC/BuD,GACFlE,GAAiB/B,EAAYnD,KAC7BzG,EAAYyG,EAAI9I,QAChBqC,EAAYyP,IACb3F,GAAcrD,IAAsB,KAAdA,EAAI9I,OACZ,KAAf8R,GACCzR,MAAMC,QAAQwR,KAAgBA,EAAWrO,OACtC0O,EAAoB5H,EAAa6H,KACrC,KACAvR,EACA2J,EACA9B,GAEI2J,EAAmB,CACvBC,EACAC,EACAC,EACAC,EAAmBzO,EACnB0O,EAAmB1O,KAEnB,MAAMsF,EAAUgJ,EAAYC,EAAmBC,EAC/C9J,EAAM7H,GAAQ,CACZf,KAAMwS,EAAYG,EAAUC,EAC5BpJ,UACAR,SACGqJ,EAAkBG,EAAYG,EAAUC,EAASpJ,KAIxD,GACEiI,GACKlR,MAAMC,QAAQwR,KAAgBA,EAAWrO,OAC1C+N,KACGS,IAAsBC,GAAWhS,EAAkB4R,KACnDzO,EAAUyO,KAAgBA,GAC1BnD,IAAehB,GAAiBc,GAAMpI,SACtC2L,IAAY3D,GAAcI,GAAMpI,SACvC,CACA,MAAMrG,MAAEA,EAAKsJ,QAAEA,GAAYwH,GAAUU,GACjC,CAAExR,QAASwR,EAAUlI,QAASkI,GAC9BP,GAAmBO,GAEvB,GAAIxR,IACF0I,EAAM7H,GAAQ,CACZf,KAAMkE,EACNsF,UACAR,IAAKiJ,KACFI,EAAkBnO,EAAiCsF,KAEnDkB,GAEH,OADAnB,EAAkBC,GACXZ,EAKb,KAAKwJ,GAAahS,EAAkByR,IAASzR,EAAkB0R,IAAO,CACpE,IAAIU,EACAK,EACJ,MAAMC,EAAY3B,GAAmBW,GAC/BiB,EAAY5B,GAAmBU,GAErC,GAAKzR,EAAkB4R,IAAgBjO,MAAMiO,GAUtC,CACL,MAAMgB,EACHhK,EAAyBmF,aAAe,IAAIhO,KAAK6R,GAC9CiB,EAAqBC,GACzB,IAAI/S,MAAK,IAAIA,MAAOgT,eAAiB,IAAMD,GACvCE,EAAqB,QAAZpK,EAAIhJ,KACbqT,EAAqB,QAAZrK,EAAIhJ,KAEf8G,EAASgM,EAAU5S,QAAU8R,IAC/BQ,EAAYY,EACRH,EAAkBjB,GAAciB,EAAkBH,EAAU5S,OAC5DmT,EACErB,EAAac,EAAU5S,MACvB8S,EAAY,IAAI7S,KAAK2S,EAAU5S,QAGnC4G,EAASiM,EAAU7S,QAAU8R,IAC/Ba,EAAYO,EACRH,EAAkBjB,GAAciB,EAAkBF,EAAU7S,OAC5DmT,EACErB,EAAae,EAAU7S,MACvB8S,EAAY,IAAI7S,KAAK4S,EAAU7S,YA/B2B,CAClE,MAAMoT,EACHtK,EAAyBkF,gBACzB8D,GAAcA,EAAaA,GACzB5R,EAAkB0S,EAAU5S,SAC/BsS,EAAYc,EAAcR,EAAU5S,OAEjCE,EAAkB2S,EAAU7S,SAC/B2S,EAAYS,EAAcP,EAAU7S,OA2BxC,IAAIsS,GAAaK,KACfN,IACIC,EACFM,EAAUtJ,QACVuJ,EAAUvJ,QACVtF,EACAA,IAEGwG,GAEH,OADAnB,EAAkBX,EAAM7H,GAAOyI,SACxBZ,EAKb,IACG+I,GAAaC,KACbQ,IACAtL,EAASkL,IAAgBP,GAAgBlR,MAAMC,QAAQwR,IACxD,CACA,MAAMuB,EAAkBpC,GAAmBQ,GACrC6B,EAAkBrC,GAAmBS,GACrCY,GACHpS,EAAkBmT,EAAgBrT,QACnC8R,EAAWrO,QAAU4P,EAAgBrT,MACjC2S,GACHzS,EAAkBoT,EAAgBtT,QACnC8R,EAAWrO,QAAU6P,EAAgBtT,MAEvC,IAAIsS,GAAaK,KACfN,EACEC,EACAe,EAAgB/J,QAChBgK,EAAgBhK,UAEbkB,GAEH,OADAnB,EAAkBX,EAAM7H,GAAOyI,SACxBZ,EAKb,GAAImJ,IAAYK,GAAWtL,EAASkL,GAAa,CAC/C,MAAQ9R,MAAOuT,EAAYjK,QAAEA,GAAY2H,GAAmBY,GAE5D,GAAIjD,GAAQ2E,KAAkBzB,EAAW0B,MAAMD,KAC7C7K,EAAM7H,GAAQ,CACZf,KAAMkE,EACNsF,UACAR,SACGqJ,EAAkBnO,EAAgCsF,KAElDkB,GAEH,OADAnB,EAAkBC,GACXZ,EAKb,GAAIiH,EACF,GAAIzD,EAAWyD,GAAW,CACxB,MACM8D,EAAgB1C,SADDpB,EAASmC,EAAY/K,GACKgL,GAE/C,GAAI0B,IACF/K,EAAM7H,GAAQ,IACT4S,KACAtB,EACDnO,EACAyP,EAAcnK,WAGbkB,GAEH,OADAnB,EAAkBoK,EAAcnK,SACzBZ,OAGN,GAAItI,EAASuP,GAAW,CAC7B,IAAI+D,EAAmB,CAAA,EAEvB,IAAK,MAAMxR,KAAOyN,EAAU,CAC1B,IAAK3D,EAAc0H,KAAsBlJ,EACvC,MAGF,MAAMiJ,EAAgB1C,SACdpB,EAASzN,GAAK4P,EAAY/K,GAChCgL,EACA7P,GAGEuR,IACFC,EAAmB,IACdD,KACAtB,EAAkBjQ,EAAKuR,EAAcnK,UAG1CD,EAAkBoK,EAAcnK,SAE5BkB,IACF9B,EAAM7H,GAAQ6S,IAKpB,IAAK1H,EAAc0H,KACjBhL,EAAM7H,GAAQ,CACZiI,IAAKiJ,KACF2B,IAEAlJ,GACH,OAAO9B,EAOf,OADAW,GAAkB,GACXX,GCnMT,MAAMiL,GAAiB,CACrBzE,KAAMnL,EACN6P,eAAgB7P,EAChB8P,kBAAkB,GAGd,SAAUC,GAKdxO,EAAkE,IAUlE,IAwCIyO,EAxCAtK,EAAW,IACVkK,MACArO,GAEDM,EAAsC,CACxCoO,YAAa,EACbjO,SAAS,EACTkO,SAAS,EACTjO,UAAWkG,EAAWzC,EAAS7E,eAC/BwB,cAAc,EACd8N,aAAa,EACbC,cAAc,EACdC,oBAAoB,EACpB/N,SAAS,EACTH,cAAe,CAAA,EACfD,YAAa,CAAA,EACbE,iBAAkB,CAAA,EAClBG,OAAQmD,EAASnD,QAAU,CAAA,EAC3Bd,SAAUiE,EAASjE,WAAY,GAE7ByD,EAAqB,CAAA,EACrBpE,GACFzE,EAASqJ,EAAS7E,gBAAkBxE,EAASqJ,EAAS/B,UAClDpG,EAAYmI,EAAS7E,eAAiB6E,EAAS/B,SAC/C,CAAA,EACFC,EAAc8B,EAAS3B,iBACtB,CAAA,EACAxG,EAAYuD,GACb+E,EAAS,CACXC,QAAQ,EACRF,OAAO,EACP1C,OAAO,GAELH,EAAgB,CAClB6C,MAAO,IAAI0K,IACX7O,SAAU,IAAI6O,IACdC,QAAS,IAAID,IACbrM,MAAO,IAAIqM,IACXpN,MAAO,IAAIoN,KAGTE,EAAQ,EACZ,MAAMtP,EAAiC,CACrCc,SAAS,EACTE,aAAa,EACbE,kBAAkB,EAClBD,eAAe,EACfE,cAAc,EACdC,SAAS,EACTC,QAAQ,GAEV,IAAIkO,EAA2B,IAC1BvP,GAEL,MAAMwP,EAAoC,CACxCzM,MAAO2C,IACP+J,MAAO/J,KAGHgK,EACJlL,EAASmL,eAAiB7Q,EAStB2C,EAAY0K,MAAOyD,IACvB,IACGpL,EAASjE,WACTP,EAAgBoB,SACfmO,EAAyBnO,SACzBwO,GACF,CACA,MAAMxO,EAAUoD,EAASqL,SACrB9I,SAAqB+I,KAAczO,cAC7B0O,EAAyB/L,GAAS,GAExC5C,IAAYT,EAAWS,SACzBoO,EAAUC,MAAM5J,KAAK,CACnBzE,cAMF4O,EAAsB,CAACrU,EAAkBwF,MAE1CqD,EAASjE,WACTP,EAAgBmB,cACfnB,EAAgBkB,kBAChBqO,EAAyBpO,cACzBoO,EAAyBrO,qBAE1BvF,GAASP,MAAM6U,KAAKpO,EAAO6C,QAAQwL,QAAStU,IACvCA,IACFuF,EACI9C,EAAIsC,EAAWO,iBAAkBtF,EAAMuF,GACvCuG,GAAM/G,EAAWO,iBAAkBtF,MAI3C4T,EAAUC,MAAM5J,KAAK,CACnB3E,iBAAkBP,EAAWO,iBAC7BC,cAAe4F,EAAcpG,EAAWO,sBA8ExCiP,EAAsB,CAC1BvU,EACAwU,EACArV,EACA8I,KAEA,MAAME,EAAejG,EAAIkG,EAASpI,GAElC,GAAImI,EAAO,CACT,MAAM9F,EAAeH,EACnB4E,EACA9G,EACAwB,EAAYrC,GAAS+C,EAAI8B,EAAgBhE,GAAQb,GAGnDqC,EAAYa,IACX4F,GAAQA,EAAyBwM,gBAClCD,EACI/R,EACEqE,EACA9G,EACAwU,EAAuBnS,EAAeqL,GAAcvF,EAAME,KAE5DqM,EAAc1U,EAAMqC,GAExB0G,EAAOD,OAASjD,MAId8O,EAAsB,CAC1B3U,EACA4U,EACA1F,EACA2F,EACAC,KAIA,IAAIC,GAAoB,EACpBC,GAAkB,EACtB,MAAM3L,EAA8D,CAClErJ,QAGF,IAAK4I,EAASjE,SAAU,CACtB,IAAKuK,GAAe2F,EAAa,EAC3BzQ,EAAgBc,SAAWyO,EAAyBzO,WACtD8P,EAAkBjQ,EAAWG,QAC7BH,EAAWG,QAAUmE,EAAOnE,QAAU+P,IACtCF,EAAoBC,IAAoB3L,EAAOnE,SAGjD,MAAMgQ,EAAyB1K,EAC7BtI,EAAI8B,EAAgBhE,GACpB4U,GAGFI,IAAoB9S,EAAI6C,EAAWK,YAAapF,GAChDkV,EACIpJ,GAAM/G,EAAWK,YAAapF,GAC9ByC,EAAIsC,EAAWK,YAAapF,GAAM,GACtCqJ,EAAOjE,YAAcL,EAAWK,YAChC2P,EACEA,IACE3Q,EAAgBgB,aAChBuO,EAAyBvO,cACzB4P,KAAqBE,EAG3B,GAAIhG,EAAa,CACf,MAAMiG,EAAyBjT,EAAI6C,EAAWM,cAAerF,GAExDmV,IACH1S,EAAIsC,EAAWM,cAAerF,EAAMkP,GACpC7F,EAAOhE,cAAgBN,EAAWM,cAClC0P,EACEA,IACE3Q,EAAgBiB,eAChBsO,EAAyBtO,gBACzB8P,IAA2BjG,GAInC6F,GAAqBD,GAAgBlB,EAAUC,MAAM5J,KAAKZ,GAG5D,OAAO0L,EAAoB1L,EAAS,CAAA,GAGhC+L,EAAsB,CAC1BpV,EACAwF,EACAqC,EACAL,KAMA,MAAM6N,EAAqBnT,EAAI6C,EAAWU,OAAQzF,GAC5CgU,GACH5P,EAAgBoB,SAAWmO,EAAyBnO,UACrDhD,EAAUgD,IACVT,EAAWS,UAAYA,EAhOzB,IAAqBI,EA6OrB,GAXIgD,EAAS0M,YAAczN,GAlONjC,EAmOW,IAzHb,EAAC5F,EAAyB6H,KAC7CpF,EAAIsC,EAAWU,OAAQzF,EAAM6H,GAC7B+L,EAAUC,MAAM5J,KAAK,CACnBxE,OAAQV,EAAWU,UAsHiB8P,CAAavV,EAAM6H,GAAvDqL,EAlODsC,IACCC,aAAa/B,GACbA,EAAQgC,WAAW9P,EAAU4P,IAiO7BtC,EAAmBtK,EAAS0M,cAE5BG,aAAa/B,GACbR,EAAqB,KACrBrL,EACIpF,EAAIsC,EAAWU,OAAQzF,EAAM6H,GAC7BiE,GAAM/G,EAAWU,OAAQzF,KAI5B6H,GAAS2C,EAAU6K,EAAoBxN,GAASwN,KAChDlK,EAAc3D,IACfwM,EACA,CACA,MAAM2B,EAAmB,IACpBnO,KACCwM,GAAqBxR,EAAUgD,GAAW,CAAEA,WAAY,GAC5DC,OAAQV,EAAWU,OACnBzF,QAGF+E,EAAa,IACRA,KACA4Q,GAGL/B,EAAUC,MAAM5J,KAAK0L,KAInBzB,EAAa3D,MAAOvQ,IACxBoU,EAAoBpU,GAAM,GAC1B,MAAMsC,QAAesG,EAASqL,SAC5BnN,EACA8B,EAASgN,QdzaA,EACbrG,EACAnH,EACA2L,EACAtD,KAEA,MAAMlE,EAAiD,CAAA,EAEvD,IAAK,MAAMvM,KAAQuP,EAAa,CAC9B,MAAMpH,EAAejG,EAAIkG,EAASpI,GAElCmI,GAAS1F,EAAI8J,EAAQvM,EAAMmI,EAAME,IAGnC,MAAO,CACL0L,eACAhU,MAAO,IAAIwP,GACXhD,SACAkE,8BcwZEoF,CACE7V,GAAQiG,EAAO6C,MACfV,EACAQ,EAASmL,aACTnL,EAAS6H,4BAIb,OADA2D,EAAoBpU,GACbsC,GAoBH6R,EAA2B5D,MAC/BhE,EACAuJ,EACAF,EAEI,CACFG,OAAO,MAGT,IAAK,MAAM/V,KAAQuM,EAAQ,CACzB,MAAMpE,EAAQoE,EAAOvM,GAErB,GAAImI,EAAO,CACT,MAAME,GAAEA,KAAOuM,GAAezM,EAE9B,GAAIE,EAAI,CACN,MAAM2N,EAAmB/P,EAAOkB,MAAMlH,IAAIoI,EAAGrI,MACvCiW,EACJ9N,EAAME,IAAMuG,GAAsBzG,EAAgBE,IAEhD4N,GAAqB7R,EAAgBkB,kBACvC8O,EAAoB,CAACpU,IAAO,GAG9B,MAAMkW,QAAmB5F,GACvBnI,EACAlC,EAAOtB,SACPmC,EACAgN,EACAlL,EAAS6H,4BAA8BqF,EACvCE,GAOF,GAJIC,GAAqB7R,EAAgBkB,kBACvC8O,EAAoB,CAACpU,IAGnBkW,EAAW7N,EAAGrI,QAChB4V,EAAQG,OAAQ,EACZD,GACF,OAIHA,IACE5T,EAAIgU,EAAY7N,EAAGrI,MAChBgW,EACEjG,GACEhL,EAAWU,OACXyQ,EACA7N,EAAGrI,MAELyC,EAAIsC,EAAWU,OAAQ4C,EAAGrI,KAAMkW,EAAW7N,EAAGrI,OAChD8L,GAAM/G,EAAWU,OAAQ4C,EAAGrI,QAGnCmL,EAAcyJ,UACNT,EACLS,EACAkB,EACAF,IAKR,OAAOA,EAAQG,OAiBXd,EAAwB,CAACjV,EAAMU,KAClCkI,EAASjE,WACT3E,GAAQU,GAAQ+B,EAAIqE,EAAa9G,EAAMU,IACvC8J,EAAU2L,IAAanS,IAEpB4C,EAAyC,CAC7C7G,EACAsC,EACA8D,IAEAH,EACEjG,EACAkG,EACA,IACM8C,EAAOD,MACPhC,EACAtF,EAAYa,GACV2B,EACA+B,EAAShG,GACP,CAAEA,CAACA,GAAQsC,GACXA,GAEV8D,EACA9D,GAcEqS,EAAgB,CACpB1U,EACAb,EACA4N,EAA0B,CAAA,KAE1B,MAAM5E,EAAejG,EAAIkG,EAASpI,GAClC,IAAI4U,EAAsBzV,EAE1B,GAAIgJ,EAAO,CACT,MAAM0G,EAAiB1G,EAAME,GAEzBwG,KACDA,EAAelK,UACdlC,EAAIqE,EAAa9G,EAAMkN,GAAgB/N,EAAO0P,IAEhD+F,EACEtJ,GAAcuD,EAAe5G,MAAQ5I,EAAkBF,GACnD,GACAA,EAEFuM,GAAiBmD,EAAe5G,KAClC,IAAI4G,EAAe5G,IAAI8E,SAASuH,QAC7B8B,GACEA,EAAUC,SACTzB,EACA3J,SAASmL,EAAUjX,QAEhB0P,EAAejB,KACpB7O,EAAgB8P,EAAe5G,KACjC4G,EAAejB,KAAK0G,QAASgC,IACtBA,EAAY7B,gBAAmB6B,EAAY3R,WAC1CnF,MAAMC,QAAQmV,GAChB0B,EAAYzW,UAAY+U,EAAW7F,KAChCrO,GAAiBA,IAAS4V,EAAYnX,OAGzCmX,EAAYzW,QACV+U,IAAe0B,EAAYnX,SAAWyV,KAK9C/F,EAAejB,KAAK0G,QACjBiC,GACEA,EAAS1W,QAAU0W,EAASpX,QAAUyV,GAGpCxJ,EAAYyD,EAAe5G,KACpC4G,EAAe5G,IAAI9I,MAAQ,IAE3B0P,EAAe5G,IAAI9I,MAAQyV,EAEtB/F,EAAe5G,IAAIhJ,MACtB2U,EAAUC,MAAM5J,KAAK,CACnBjK,OACA6G,OAAQpG,EAAYqG,QAO7BiG,EAAQ8H,aAAe9H,EAAQyJ,cAC9B7B,EACE3U,EACA4U,EACA7H,EAAQyJ,YACRzJ,EAAQ8H,aACR,GAGJ9H,EAAQ0J,gBAAkBC,EAAQ1W,IAG9B2W,EAAY,CAKhB3W,EACAb,EACA4N,KAEA,IAAK,MAAM6J,KAAYzX,EAAO,CAC5B,IAAKA,EAAMgC,eAAeyV,GACxB,OAEF,MAAMhC,EAAazV,EAAMyX,GACnBrQ,EAAYvG,EAAO,IAAM4W,EACzBzO,EAAQjG,EAAIkG,EAAS7B,IAE1BN,EAAOkB,MAAMlH,IAAID,IAChBT,EAASqV,IACRzM,IAAUA,EAAME,MAClBnJ,EAAa0V,GACV+B,EAAUpQ,EAAWqO,EAAY7H,GACjC2H,EAAcnO,EAAWqO,EAAY7H,KAIvC8J,EAA0C,CAC9C7W,EACAb,EACA4N,EAAU,CAAA,KAEV,MAAM5E,EAAQjG,EAAIkG,EAASpI,GACrB0Q,EAAezK,EAAOkB,MAAMlH,IAAID,GAChC8W,EAAarW,EAAYtB,GAE/BsD,EAAIqE,EAAa9G,EAAM8W,GAEnBpG,GACFkD,EAAUzM,MAAM8C,KAAK,CACnBjK,OACA6G,OAAQpG,EAAYqG,MAInB1C,EAAgBc,SACfd,EAAgBgB,aAChBuO,EAAyBzO,SACzByO,EAAyBvO,cAC3B2H,EAAQ8H,aAERjB,EAAUC,MAAM5J,KAAK,CACnBjK,OACAoF,YAAauH,GAAe3I,EAAgB8C,GAC5C5B,QAAS+P,EAAUjV,EAAM8W,OAI7B3O,GAAUA,EAAME,IAAOhJ,EAAkByX,GAErCpC,EAAc1U,EAAM8W,EAAY/J,GADhC4J,EAAU3W,EAAM8W,EAAY/J,GAIlCkC,GAAUjP,EAAMiG,IAAW2N,EAAUC,MAAM5J,KAAK,IAAKlF,IACrD6O,EAAUC,MAAM5J,KAAK,CACnBjK,KAAM+I,EAAOD,MAAQ9I,OAAO0B,EAC5BmF,OAAQpG,EAAYqG,MAIlBgB,EAA0ByI,MAAO5Q,IACrCoJ,EAAOD,OAAQ,EACf,MAAMlJ,EAASD,EAAMC,OACrB,IAAII,EAAeJ,EAAOI,KACtB+W,GAAsB,EAC1B,MAAM5O,EAAejG,EAAIkG,EAASpI,GAC5BgX,EAA8BpC,IAClCmC,EACEE,OAAOjU,MAAM4R,IACZ1V,EAAa0V,IAAe5R,MAAM4R,EAAW/J,YAC9CL,EAAUoK,EAAY1S,EAAI4E,EAAa9G,EAAM4U,KAE3CsC,EAA6B9I,GAAmBxF,EAASyF,MACzD8I,EAA4B/I,GAChCxF,EAASmK,gBAGX,GAAI5K,EAAO,CACT,IAAIN,EACArC,EACJ,MAAMoP,EAAahV,EAAOX,KACtByO,GAAcvF,EAAME,IACpB3I,EAAcC,GACZuP,EACJvP,EAAMV,OAASgE,GAAetD,EAAMV,OAASgE,EACzCmU,KC9uBIrK,ED+uBQ5E,EAAME,IC9uBpBS,QACPiE,EAAQ4D,UACP5D,EAAQ+D,KACR/D,EAAQgE,KACRhE,EAAQ6D,WACR7D,EAAQ8D,WACR9D,EAAQiE,SACRjE,EAAQ+B,WDwuBDlG,EAASqL,UACT/R,EAAI6C,EAAWU,OAAQzF,IACvBmI,EAAME,GAAGgP,OElvBL,EACbnI,EACAtH,EACAyL,EACAN,EAIA1E,KAEIA,EAAKI,WAEG4E,GAAehF,EAAKK,YACrB9G,GAAasH,IACbmE,EAAcN,EAAexE,SAAWF,EAAKE,WAC9CW,IACCmE,EAAcN,EAAevE,WAAaH,EAAKG,aACjDU,GFkuBHoI,CACEpI,EACAhN,EAAI6C,EAAWM,cAAerF,GAC9B+E,EAAWsO,YACX8D,EACAD,GAEEK,EAAUtI,GAAUjP,EAAMiG,EAAQiJ,GAExCzM,EAAIqE,EAAa9G,EAAM4U,GAEnB1F,GACF/G,EAAME,GAAGL,QAAUG,EAAME,GAAGL,OAAOrI,GACnCuT,GAAsBA,EAAmB,IAChC/K,EAAME,GAAGP,UAClBK,EAAME,GAAGP,SAASnI,GAGpB,MAAM6H,EAAamN,EAAoB3U,EAAM4U,EAAY1F,GAEnD4F,GAAgB3J,EAAc3D,IAAe+P,EASnD,IAPCrI,GACC0E,EAAUC,MAAM5J,KAAK,CACnBjK,OACAf,KAAMU,EAAMV,KACZ4H,OAAQpG,EAAYqG,KAGpBsQ,EAWF,OAVIhT,EAAgBoB,SAAWmO,EAAyBnO,WAChC,WAAlBoD,EAASyF,KACPa,GACFrJ,IAEQqJ,GACVrJ,KAKFiP,GACAlB,EAAUC,MAAM5J,KAAK,CAAEjK,UAAUuX,EAAU,CAAA,EAAK/P,IAMpD,IAFC0H,GAAeqI,GAAW3D,EAAUC,MAAM5J,KAAK,IAAKlF,IAEjD6D,EAASqL,SAAU,CACrB,MAAMxO,OAAEA,SAAiByO,EAAW,CAAClU,IAIrC,GAFAgX,EAA2BpC,GAEvBmC,EAAqB,CACvB,MAAMS,EAA4B9H,GAChC3K,EAAWU,OACX2C,EACApI,GAEIyX,EAAoB/H,GACxBjK,EACA2C,EACAoP,EAA0BxX,MAAQA,GAGpC6H,EAAQ4P,EAAkB5P,MAC1B7H,EAAOyX,EAAkBzX,KAEzBwF,EAAU2F,EAAc1F,SAG1B2O,EAAoB,CAACpU,IAAO,GAC5B6H,SACQyI,GACJnI,EACAlC,EAAOtB,SACPmC,EACAgN,EACAlL,EAAS6H,4BAEXzQ,GACFoU,EAAoB,CAACpU,IAErBgX,EAA2BpC,GAEvBmC,IACElP,EACFrC,GAAU,GAEVpB,EAAgBoB,SAChBmO,EAAyBnO,WAEzBA,QAAgB2O,EAAyB/L,GAAS,KAKpD2O,IACF5O,EAAME,GAAGgP,MACPX,EACEvO,EAAME,GAAGgP,MAIbjC,EAAoBpV,EAAMwF,EAASqC,EAAOL,IC31BnC,IAACuF,GDg2BR2K,EAAc,CAACzP,EAAU5G,KAC7B,GAAIa,EAAI6C,EAAWU,OAAQpE,IAAQ4G,EAAIK,MAErC,OADAL,EAAIK,QACG,GAKLoO,EAAwCnG,MAAOvQ,EAAM+M,EAAU,CAAA,KACnE,IAAIvH,EACAqN,EACJ,MAAM8E,EAAa9N,EAAsB7J,GAEzC,GAAI4I,EAASqL,SAAU,CACrB,MAAMxO,OAhb0B8K,OAAOxQ,IACzC,MAAM0F,OAAEA,SAAiByO,EAAWnU,GAEpC,GAAIA,EACF,IAAK,MAAMC,KAAQD,EAAO,CACxB,MAAM8H,EAAQ3F,EAAIuD,EAAQzF,GAC1B6H,EACIpF,EAAIsC,EAAWU,OAAQzF,EAAM6H,GAC7BiE,GAAM/G,EAAWU,OAAQzF,QAG/B+E,EAAWU,OAASA,EAGtB,OAAOA,GAkagBmS,CACnBpW,EAAYxB,GAAQA,EAAO2X,GAG7BnS,EAAU2F,EAAc1F,GACxBoN,EAAmB7S,GACd2X,EAAWxI,KAAMnP,GAASkC,EAAIuD,EAAQzF,IACvCwF,OACKxF,GACT6S,SACQgF,QAAQC,IACZH,EAAWrR,IAAIiK,MAAOhK,IACpB,MAAM4B,EAAQjG,EAAIkG,EAAS7B,GAC3B,aAAa4N,EACXhM,GAASA,EAAME,GAAK,CAAE9B,CAACA,GAAY4B,GAAUA,OAInDgI,MAAMtO,UACLgR,GAAqB9N,EAAWS,UAAYK,KAE/CgN,EAAmBrN,QAAgB2O,EAAyB/L,GAqB9D,OAlBAwL,EAAUC,MAAM5J,KAAK,KACdlE,EAAS/F,KACZoE,EAAgBoB,SAAWmO,EAAyBnO,UACpDA,IAAYT,EAAWS,QACrB,CAAA,EACA,CAAExF,WACF4I,EAASqL,WAAajU,EAAO,CAAEwF,WAAY,GAC/CC,OAAQV,EAAWU,SAGrBsH,EAAQgL,cACLlF,GACDvD,GACElH,EACAsP,EACA1X,EAAO2X,EAAa1R,EAAO6C,OAGxB+J,GAGHsD,EACJwB,IAIA,MAAM9Q,EAAS,IACTkC,EAAOD,MAAQhC,EAAc9C,GAGnC,OAAOxC,EAAYmW,GACf9Q,EACAd,EAAS4R,GACPzV,EAAI2E,EAAQ8Q,GACZA,EAAWrR,IAAKtG,GAASkC,EAAI2E,EAAQ7G,KAGvCgY,GAAoD,CACxDhY,EACA2D,KAAS,CAET+D,UAAWxF,GAAKyB,GAAaoB,GAAYU,OAAQzF,GACjDkF,UAAWhD,GAAKyB,GAAaoB,GAAYK,YAAapF,GACtD6H,MAAO3F,GAAKyB,GAAaoB,GAAYU,OAAQzF,GAC7CuF,eAAgBrD,EAAI6C,EAAWO,iBAAkBtF,GACjD4H,YAAa1F,GAAKyB,GAAaoB,GAAYM,cAAerF,KActDiY,GAA0C,CAACjY,EAAM6H,EAAOkF,KAC5D,MAAM9E,GAAO/F,EAAIkG,EAASpI,EAAM,CAAEqI,GAAI,KAAMA,IAAM,CAAA,GAAIJ,IAChDiQ,EAAehW,EAAI6C,EAAWU,OAAQzF,IAAS,CAAA,GAG7CiI,IAAKkQ,EAAU1P,QAAEA,EAAOxJ,KAAEA,KAASmZ,GAAoBF,EAE/DzV,EAAIsC,EAAWU,OAAQzF,EAAM,IACxBoY,KACAvQ,EACHI,QAGF2L,EAAUC,MAAM5J,KAAK,CACnBjK,OACAyF,OAAQV,EAAWU,OACnBD,SAAS,IAGXuH,GAAWA,EAAQgL,aAAe9P,GAAOA,EAAIK,OAASL,EAAIK,SA4BtD5C,GAA2CjB,GAC/CmP,EAAUC,MAAM1J,UAAU,CACxBF,KACEtG,IGn/BO,IACb3D,EACAqY,EACAzT,EAFA5E,EHy/B8ByE,EAAMzE,KGx/BpCqY,EHw/B0C1U,EAAU3D,KGv/BpD4E,EHu/B0DH,EAAMG,MGr/B/D5E,GACAqY,GACDrY,IAASqY,IACTxO,EAAsB7J,GAAMmP,KACzBmJ,GACCA,IACC1T,EACG0T,IAAgBD,EAChBC,EAAYjJ,WAAWgJ,IACvBA,EAAWhJ,WAAWiJ,OTPjB,EACbC,EAIAnU,EACAS,EACAf,KAEAe,EAAgB0T,GAChB,MAAMvY,KAAEA,KAAS2D,GAAc4U,EAE/B,OACEpN,EAAcxH,IACdM,OAAOqF,KAAK3F,GAAWf,QAAUqB,OAAOqF,KAAKlF,GAAiBxB,QAC9DqB,OAAOqF,KAAK3F,GAAWoL,KACpB1N,GACC+C,EAAgB/C,OACdyC,GAAUZ,KMk+BVsV,CACE7U,EACCc,EAAMd,WAA+BS,EACtCqU,GACAhU,EAAMiU,eAGRjU,EAAMmB,SAAS,CACbiB,OAAQ,IAAKC,MACV/B,KACApB,OAIR0G,YAcCpB,GAA8C,CAACjJ,EAAM+M,EAAU,CAAA,KACnE,IAAK,MAAMxG,KAAavG,EAAO6J,EAAsB7J,GAAQiG,EAAO6C,MAClE7C,EAAO6C,MAAM6P,OAAOpS,GACpBN,EAAOkB,MAAMwR,OAAOpS,GAEfwG,EAAQ6L,YACX9M,GAAM1D,EAAS7B,GACfuF,GAAMhF,EAAaP,KAGpBwG,EAAQ8L,WAAa/M,GAAM/G,EAAWU,OAAQc,IAC9CwG,EAAQ+L,WAAahN,GAAM/G,EAAWK,YAAamB,IACnDwG,EAAQgM,aAAejN,GAAM/G,EAAWM,cAAekB,IACvDwG,EAAQiM,kBACPlN,GAAM/G,EAAWO,iBAAkBiB,IACpCqC,EAAS3B,mBACP8F,EAAQkM,kBACTnN,GAAM9H,EAAgBuC,GAG1BqN,EAAUC,MAAM5J,KAAK,CACnBpD,OAAQpG,EAAYqG,KAGtB8M,EAAUC,MAAM5J,KAAK,IAChBlF,KACEgI,EAAQ+L,UAAiB,CAAE5T,QAAS+P,KAAhB,CAAA,KAG1BlI,EAAQmM,aAAerT,KAGpBqD,GAAgE,EACpEvE,WACA3E,YAGGwC,EAAUmC,IAAaoE,EAAOD,OAC7BnE,GACFsB,EAAOtB,SAAS1E,IAAID,MAEpB2E,EAAWsB,EAAOtB,SAAS0B,IAAIrG,GAAQiG,EAAOtB,SAASgU,OAAO3Y,KAI5DsH,GAA0C,CAACtH,EAAM+M,EAAU,CAAA,KAC/D,IAAI5E,EAAQjG,EAAIkG,EAASpI,GACzB,MAAMmZ,EACJ3W,EAAUuK,EAAQpI,WAAanC,EAAUoG,EAASjE,UAwBpD,OAtBAlC,EAAI2F,EAASpI,EAAM,IACbmI,GAAS,CAAA,EACbE,GAAI,IACEF,GAASA,EAAME,GAAKF,EAAME,GAAK,CAAEJ,IAAK,CAAEjI,SAC5CA,OACA8I,OAAO,KACJiE,KAGP9G,EAAO6C,MAAMzC,IAAIrG,GAEbmI,EACFe,GAAkB,CAChBvE,SAAUnC,EAAUuK,EAAQpI,UACxBoI,EAAQpI,SACRiE,EAASjE,SACb3E,SAGFuU,EAAoBvU,GAAM,EAAM+M,EAAQ5N,OAGnC,IACDga,EACA,CAAExU,SAAUoI,EAAQpI,UAAYiE,EAASjE,UACzC,MACAiE,EAASwQ,YACT,CACEzI,WAAY5D,EAAQ4D,SACpBG,IAAK7C,GAAalB,EAAQ+D,KAC1BC,IAAK9C,GAAalB,EAAQgE,KAC1BF,UAAW5C,GAAqBlB,EAAQ8D,WACxCD,UAAW3C,GAAalB,EAAQ6D,WAChCI,QAAS/C,GAAalB,EAAQiE,UAEhC,GACJhR,OACA8H,WACAE,OAAQF,EACRG,IAAMA,IACJ,GAAIA,EAAK,CACPX,GAAStH,EAAM+M,GACf5E,EAAQjG,EAAIkG,EAASpI,GAErB,MAAMqZ,EAAW7X,EAAYyG,EAAI9I,QAC7B8I,EAAIqR,kBACDrR,EAAIqR,iBAAiB,yBAAyB,IAEjDrR,EACEsR,EIvnCD,CAACtR,GACd0D,GAAa1D,IAAQlJ,EAAgBkJ,GJsnCLmJ,CAAkBiI,GACpCzL,EAAOzF,EAAME,GAAGuF,MAAQ,GAE9B,GACE2L,EACI3L,EAAKmB,KAAM/B,GAAgBA,IAAWqM,GACtCA,IAAalR,EAAME,GAAGJ,IAE1B,OAGFxF,EAAI2F,EAASpI,EAAM,CACjBqI,GAAI,IACCF,EAAME,MACLkR,EACA,CACE3L,KAAM,IACDA,EAAKhM,OAAOgK,IACfyN,KACI7Z,MAAMC,QAAQyC,EAAI8B,EAAgBhE,IAAS,CAAC,IAAM,IAExDiI,IAAK,CAAEhJ,KAAMoa,EAASpa,KAAMe,SAE9B,CAAEiI,IAAKoR,MAIf9E,EAAoBvU,GAAM,OAAO0B,EAAW2X,QAE5ClR,EAAQjG,EAAIkG,EAASpI,EAAM,CAAA,GAEvBmI,EAAME,KACRF,EAAME,GAAGS,OAAQ,IAGlBF,EAAS3B,kBAAoB8F,EAAQ9F,qBAClCnH,EAAmBmG,EAAOkB,MAAOnH,KAAS+I,EAAOC,SACnD/C,EAAOwN,QAAQpN,IAAIrG,MAMvBwZ,GAAc,IAClB5Q,EAASoK,kBACT1D,GAAsBlH,EAASsP,EAAazR,EAAO6C,OAyB/C2Q,GACJ,CAACC,EAASC,IAAcpJ,MAAOqJ,IAC7B,IAAIC,EACAD,IACFA,EAAEE,gBAAkBF,EAAEE,iBACrBF,EAA+BG,SAC7BH,EAA+BG,WAEpC,IAAIC,EACFvZ,EAAYqG,GAMd,GAJA8M,EAAUC,MAAM5J,KAAK,CACnBqJ,cAAc,IAGZ1K,EAASqL,SAAU,CACrB,MAAMxO,OAAEA,EAAMoB,OAAEA,SAAiBqN,IACjCnP,EAAWU,OAASA,EACpBuU,EAAcvZ,EAAYoG,cAEpBsN,EAAyB/L,GAGjC,GAAInC,EAAOtB,SAASsV,KAClB,IAAK,MAAMja,KAAQiG,EAAOtB,SACxBmH,GAAMkO,EAAaha,GAMvB,GAFA8L,GAAM/G,EAAWU,OAAQ,QAErB0F,EAAcpG,EAAWU,QAAS,CACpCmO,EAAUC,MAAM5J,KAAK,CACnBxE,OAAQ,CAAA,IAEV,UACQiU,EAAQM,EAAmCJ,GACjD,MAAO/R,GACPgS,EAAehS,QAGb8R,SACIA,EAAU,IAAK5U,EAAWU,QAAUmU,GAE5CJ,KACA9D,WAAW8D,IAUb,GAPA5F,EAAUC,MAAM5J,KAAK,CACnBoJ,aAAa,EACbC,cAAc,EACdC,mBAAoBpI,EAAcpG,EAAWU,UAAYoU,EACzD1G,YAAapO,EAAWoO,YAAc,EACtC1N,OAAQV,EAAWU,SAEjBoU,EACF,MAAMA,GAoCNK,GAAqC,CACzChU,EACAiU,EAAmB,CAAA,KAEnB,MAAMC,EAAgBlU,EAAazF,EAAYyF,GAAclC,EACvDqW,EAAqB5Z,EAAY2Z,GACjCE,EAAqBnP,EAAcjF,GACnCW,EAASyT,EAAqBtW,EAAiBqW,EAMrD,GAJKF,EAAiBI,oBACpBvW,EAAiBoW,IAGdD,EAAiBK,WAAY,CAChC,GAAIL,EAAiBM,gBAAiB,CACpC,MAAMC,EAAgB,IAAIlH,IAAI,IACzBvN,EAAO6C,SACP7E,OAAOqF,KAAKqD,GAAe3I,EAAgB8C,MAEhD,IAAK,MAAMP,KAAa/G,MAAM6U,KAAKqG,GACjCxY,EAAI6C,EAAWK,YAAamB,GACxB9D,EAAIoE,EAAQN,EAAWrE,EAAI4E,EAAaP,IACxCsQ,EACEtQ,EACArE,EAAI2E,EAAQN,QAGf,CACL,GAAIlG,GAASmB,EAAY0E,GACvB,IAAK,MAAMlG,KAAQiG,EAAO6C,MAAO,CAC/B,MAAMX,EAAQjG,EAAIkG,EAASpI,GAC3B,GAAImI,GAASA,EAAME,GAAI,CACrB,MAAMwG,EAAiBrP,MAAMC,QAAQ0I,EAAME,GAAGuF,MAC1CzF,EAAME,GAAGuF,KAAK,GACdzF,EAAME,GAAGJ,IAEb,GAAIqD,GAAcuD,GAAiB,CACjC,MAAM8L,EAAO9L,EAAe+L,QAAQ,QACpC,GAAID,EAAM,CACRA,EAAKE,QACL,SAOV,GAAIV,EAAiBW,cACnB,IAAK,MAAMvU,KAAaN,EAAO6C,MAC7B+N,EACEtQ,EACArE,EAAI2E,EAAQN,SAIhB6B,EAAU,CAAA,EAIdtB,EAAc8B,EAAS3B,iBACnBkT,EAAiBI,kBACd9Z,EAAYuD,GACZ,CAAA,EACFvD,EAAYoG,GAEjB+M,EAAUzM,MAAM8C,KAAK,CACnBpD,OAAQ,IAAKA,KAGf+M,EAAUC,MAAM5J,KAAK,CACnBpD,OAAQ,IAAKA,KAIjBZ,EAAS,CACP6C,MAAOqR,EAAiBM,gBAAkBxU,EAAO6C,MAAQ,IAAI0K,IAC7DC,QAAS,IAAID,IACbrM,MAAO,IAAIqM,IACX7O,SAAU,IAAI6O,IACdpN,MAAO,IAAIoN,IACXhN,UAAU,EACV8B,MAAO,IAGTS,EAAOD,OACJ1E,EAAgBoB,WACf2U,EAAiBjB,eACjBiB,EAAiBM,gBAErB1R,EAAO3C,QAAUwC,EAAS3B,iBAE1B2M,EAAUC,MAAM5J,KAAK,CACnBkJ,YAAagH,EAAiBY,gBAC1BhW,EAAWoO,YACX,EACJjO,SAASoV,IAELH,EAAiBrB,UACf/T,EAAWG,WAETiV,EAAiBI,mBAChB/P,EAAUtE,EAAYlC,KAE/BqP,cAAa8G,EAAiBa,iBAC1BjW,EAAWsO,YAEfjO,YAAakV,EACT,CAAA,EACAH,EAAiBM,gBACfN,EAAiBI,mBAAqBzT,EACpC6F,GAAe3I,EAAgB8C,GAC/B/B,EAAWK,YACb+U,EAAiBI,mBAAqBrU,EACpCyG,GAAe3I,EAAgBkC,GAC/BiU,EAAiBrB,UACf/T,EAAWK,YACX,CAAA,EACVC,cAAe8U,EAAiBpB,YAC5BhU,EAAWM,cACX,CAAA,EACJI,OAAQ0U,EAAiBc,WAAalW,EAAWU,OAAS,CAAA,EAC1D8N,qBAAoB4G,EAAiBe,wBACjCnW,EAAWwO,mBAEfD,cAAc,KAIZuH,GAAoC,CAAC3U,EAAYiU,IACrDD,GACE7O,EAAWnF,GACNA,EAAwBY,GACzBZ,EACJiU,GAqBE1B,GACJ9C,IAEA5Q,EAAa,IACRA,KACA4Q,IAaDjR,GAAU,CACdd,QAAS,CACP0D,YACA2B,cACA+O,iBACAyB,gBACAxB,YACAvS,cACAwO,aACAsF,eACA5S,YACAqO,YACApP,YACAsV,eAzvC0C,CAC5Cnb,EACA6G,EAAS,GACTuU,EACAC,EACAC,GAAkB,EAClBC,GAA6B,KAE7B,GAAIF,GAAQD,IAAWxS,EAASjE,SAAU,CAExC,GADAoE,EAAOC,QAAS,EACZuS,GAA8B/b,MAAMC,QAAQyC,EAAIkG,EAASpI,IAAQ,CACnE,MAAMga,EAAcoB,EAAOlZ,EAAIkG,EAASpI,GAAOqb,EAAKG,KAAMH,EAAKI,MAC/DH,GAAmB7Y,EAAI2F,EAASpI,EAAMga,GAGxC,GACEuB,GACA/b,MAAMC,QAAQyC,EAAI6C,EAAWU,OAAQzF,IACrC,CACA,MAAMyF,EAAS2V,EACblZ,EAAI6C,EAAWU,OAAQzF,GACvBqb,EAAKG,KACLH,EAAKI,MAEPH,GAAmB7Y,EAAIsC,EAAWU,OAAQzF,EAAMyF,GKlPzC,EAAIwC,EAAQjI,MACxB2B,EAAQO,EAAI+F,EAAKjI,IAAO4C,QAAUkJ,GAAM7D,EAAKjI,ILkPxC0b,CAAgB3W,EAAWU,OAAQzF,GAGrC,IACGoE,EAAgBiB,eACfsO,EAAyBtO,gBAC3BkW,GACA/b,MAAMC,QAAQyC,EAAI6C,EAAWM,cAAerF,IAC5C,CACA,MAAMqF,EAAgB+V,EACpBlZ,EAAI6C,EAAWM,cAAerF,GAC9Bqb,EAAKG,KACLH,EAAKI,MAEPH,GAAmB7Y,EAAIsC,EAAWM,cAAerF,EAAMqF,IAGrDjB,EAAgBgB,aAAeuO,EAAyBvO,eAC1DL,EAAWK,YAAcuH,GAAe3I,EAAgB8C,IAG1D8M,EAAUC,MAAM5J,KAAK,CACnBjK,OACAkF,QAAS+P,EAAUjV,EAAM6G,GACzBzB,YAAaL,EAAWK,YACxBK,OAAQV,EAAWU,OACnBD,QAAST,EAAWS,eAGtB/C,EAAIqE,EAAa9G,EAAM6G,IAosCvBqC,qBACAyS,WA1rCgBlW,IAClBV,EAAWU,OAASA,EACpBmO,EAAUC,MAAM5J,KAAK,CACnBxE,OAAQV,EAAWU,OACnBD,SAAS,KAurCToW,eA55BF5b,GAEA2B,EACEO,EACE6G,EAAOD,MAAQhC,EAAc9C,EAC7BhE,EACA4I,EAAS3B,iBAAmB/E,EAAI8B,EAAgBhE,EAAM,IAAM,KAu5B9Dka,UACA2B,oBA3BwB,IAC1BxQ,EAAWzC,EAAS7E,gBACnB6E,EAAS7E,gBAA6B+X,KAAMjV,IAC3CgU,GAAMhU,EAAQ+B,EAASmT,cACvBnI,EAAUC,MAAM5J,KAAK,CACnB9E,WAAW,MAuBb4B,iBAx8BqB,KACvB,IAAK,MAAM/G,KAAQiG,EAAOwN,QAAS,CACjC,MAAMtL,EAAejG,EAAIkG,EAASpI,GAElCmI,IACGA,EAAME,GAAGuF,KACNzF,EAAME,GAAGuF,KAAKuC,MAAOlI,IAAS2D,GAAK3D,KAClC2D,GAAKzD,EAAME,GAAGJ,OACnBgB,GAAWjJ,GAGfiG,EAAOwN,QAAU,IAAID,KA87BnBwI,aAnTkBrX,IAChBnC,EAAUmC,KACZiP,EAAUC,MAAM5J,KAAK,CAAEtF,aACvB2K,GACElH,EACA,CAACH,EAAKjI,KACJ,MAAMyP,EAAsBvN,EAAIkG,EAASpI,GACrCyP,IACFxH,EAAItD,SAAW8K,EAAapH,GAAG1D,UAAYA,EAEvCnF,MAAMC,QAAQgQ,EAAapH,GAAGuF,OAChC6B,EAAapH,GAAGuF,KAAK0G,QAASpD,IAC5BA,EAASvM,SAAW8K,EAAapH,GAAG1D,UAAYA,MAKxD,GACA,KAkSFiP,YACAxP,kBACA,WAAIgE,GACF,OAAOA,GAET,eAAItB,GACF,OAAOA,GAET,UAAIiC,GACF,OAAOA,GAET,UAAIA,CAAO5J,GACT4J,EAAS5J,GAEX,kBAAI6E,GACF,OAAOA,GAET,UAAIiC,GACF,OAAOA,GAET,UAAIA,CAAO9G,GACT8G,EAAS9G,GAEX,cAAI4F,GACF,OAAOA,GAET,YAAI6D,GACF,OAAOA,GAET,YAAIA,CAASzJ,GACXyJ,EAAW,IACNA,KACAzJ,KAITgL,UAtfiD1F,IACjDsE,EAAOD,OAAQ,EACf6K,EAA2B,IACtBA,KACAlP,EAAMd,WAEJ+B,GAAW,IACbjB,EACHd,UAAWgQ,KA+eb+C,UACApP,YACAmS,gBACArT,MA9iBwC,CACxCpG,EAIAqC,IAEAgJ,EAAWrL,GACP4T,EAAUC,MAAM1J,UAAU,CACxBF,KAAOgS,GACLjc,EACE4G,OAAUlF,EAAWW,GACrB4Z,KAONrV,EACE5G,EACAqC,GACA,GAyhBNwU,WACAV,YACA0E,SACAqB,WA7QkD,CAAClc,EAAM+M,EAAU,CAAA,KAC/D7K,EAAIkG,EAASpI,KACXwB,EAAYuL,EAAQ1K,cACtBwU,EAAS7W,EAAMS,EAAYyB,EAAI8B,EAAgBhE,MAE/C6W,EACE7W,EACA+M,EAAQ1K,cAEVI,EAAIuB,EAAgBhE,EAAMS,EAAYsM,EAAQ1K,gBAG3C0K,EAAQgM,aACXjN,GAAM/G,EAAWM,cAAerF,GAG7B+M,EAAQ+L,YACXhN,GAAM/G,EAAWK,YAAapF,GAC9B+E,EAAWG,QAAU6H,EAAQ1K,aACzB4S,EAAUjV,EAAMS,EAAYyB,EAAI8B,EAAgBhE,KAChDiV,KAGDlI,EAAQ8L,YACX/M,GAAM/G,EAAWU,OAAQzF,GACzBoE,EAAgBoB,SAAWK,KAG7B+N,EAAUC,MAAM5J,KAAK,IAAKlF,MAkP5BoX,YAplBqDnc,IACrDA,GACE6J,EAAsB7J,GAAMsU,QAAS8H,GACnCtQ,GAAM/G,EAAWU,OAAQ2W,IAG7BxI,EAAUC,MAAM5J,KAAK,CACnBxE,OAAQzF,EAAO+E,EAAWU,OAAS,CAAA,KA8kBrCwD,cACAgP,YACAoE,SAzG8C,CAACrc,EAAM+M,EAAU,CAAA,KAC/D,MAAM5E,EAAQjG,EAAIkG,EAASpI,GACrB6O,EAAiB1G,GAASA,EAAME,GAEtC,GAAIwG,EAAgB,CAClB,MAAMwK,EAAWxK,EAAejB,KAC5BiB,EAAejB,KAAK,GACpBiB,EAAe5G,IAEfoR,EAAS/Q,QACX+Q,EAAS/Q,QACTyE,EAAQuP,cACNjR,EAAWgO,EAAS9Q,SACpB8Q,EAAS9Q,YA6FfyP,kBAGF,MAAO,IACFtT,GACH6X,YAAa7X,GAEjB,CMvhDA,IAAA8X,GAAe,KACb,GAAsB,oBAAXC,QAA0BA,OAAOC,WAC1C,OAAOD,OAAOC,aAGhB,MAAMC,EACmB,oBAAhBC,YAA8Bxd,KAAKyd,MAA4B,IAApBD,YAAYC,MAEhE,MAAO,uCAAuC7a,QAAQ,QAAU8a,IAC9D,MAAMC,GAAqB,GAAhBC,KAAKC,SAAgBN,GAAK,GAAK,EAE1C,OAAa,KAALG,EAAWC,EAAS,EAAJA,EAAW,GAAKG,SAAS,OCRrDC,GAAe,CACbnd,EACA0C,EACAqK,EAAiC,CAAA,IAEjCA,EAAQgL,aAAevW,EAAYuL,EAAQgL,aACvChL,EAAQqQ,WACR,GAAGpd,KAAQwB,EAAYuL,EAAQsQ,YAAc3a,EAAQqK,EAAQsQ,cAC7D,GCTNC,GAAe,CAAI5c,EAAWvB,IAAwB,IACjDuB,KACAmJ,EAAsB1K,ICJ3Boe,GAAmBpe,GACjBK,MAAMC,QAAQN,GAASA,EAAMmH,IAAI,aAAmB5E,ECOxC,SAAU8b,GACtB9c,EACAgC,EACAvD,GAEA,MAAO,IACFuB,EAAKwL,MAAM,EAAGxJ,MACdmH,EAAsB1K,MACtBuB,EAAKwL,MAAMxJ,GAElB,CChBA,IAAA+a,GAAe,CACb/c,EACA2T,EACAqJ,IAEKle,MAAMC,QAAQiB,IAIfc,EAAYd,EAAKgd,MACnBhd,EAAKgd,QAAMhc,GAEbhB,EAAKid,OAAOD,EAAI,EAAGhd,EAAKid,OAAOtJ,EAAM,GAAG,IAEjC3T,GARE,GCNXkd,GAAe,CAAIld,EAAWvB,IAAwB,IACjD0K,EAAsB1K,MACtB0K,EAAsBnJ,ICY3B,IAAAmd,GAAe,CAAInd,EAAWgC,IAC5BlB,EAAYkB,GACR,GAdN,SAA4BhC,EAAWod,GACrC,IAAIC,EAAI,EACR,MAAMC,EAAO,IAAItd,GAEjB,IAAK,MAAMgC,KAASob,EAClBE,EAAKL,OAAOjb,EAAQqb,EAAG,GACvBA,IAGF,OAAOpc,EAAQqc,GAAMpb,OAASob,EAAO,EACvC,CAKMC,CACEvd,EACCmJ,EAAsBnH,GAAoBwb,KAAK,CAACC,EAAGC,IAAMD,EAAIC,ICrBtEC,GAAe,CAAI3d,EAAW4d,EAAgBC,MAC3C7d,EAAK4d,GAAS5d,EAAK6d,IAAW,CAAC7d,EAAK6d,GAAS7d,EAAK4d,KCDrDE,GAAe,CAAIxE,EAAkBtX,EAAevD,KAClD6a,EAAYtX,GAASvD,EACd6a,gBnDgDPvV,GAEAA,EAAMga,OAAOzX,EAAuDvC,WEtBtE,SAGEA,GACA,MAAMC,EAAUlB,KACTkb,EAASC,GAActb,EAAMyB,UAAS,IACvClB,QACJA,EAAUc,EAAQd,QAAOgb,SACzBA,EAAQC,SACRA,EAAQ7V,OACRA,EAAMoS,OACNA,EAAS3R,EAAYqV,QACrBA,EAAOC,QACPA,EAAOC,QACPA,EAAOP,OACPA,EAAMQ,UACNA,EAASC,eACTA,KACGC,GACD1a,EAEE2a,EAAS7O,MAAO5Q,IACpB,IAAI0f,GAAW,EACXpgB,EAAO,SAEL2E,EAAQ6V,aAAalJ,MAAO7P,IAChC,MAAM4e,EAAW,IAAIC,SACrB,IAAIC,EAAe,GAEnB,IACEA,EAAeC,KAAKC,UAAUhf,GAC9B,MAAAif,GAAM,CAER,MAAMC,EAAoBzW,EAAQvF,EAAQkD,aAE1C,IAAK,MAAMzF,KAAOue,EAChBN,EAASO,OAAOxe,EAAKue,EAAkBve,IAazC,GAVIud,SACIA,EAAS,CACble,OACAf,QACAyb,SACAkE,WACAE,iBAIAxW,EACF,IACE,MAAM8W,EAAgC,CACpChB,GAAWA,EAAQ,gBACnBC,GACA5P,KAAMhQ,GAAUA,GAASA,EAAM8L,SAAS,SAEpC8U,QAAiBC,MAAMC,OAAOjX,GAAS,CAC3CoS,SACA0D,QAAS,IACJA,KACCC,EAAU,CAAE,eAAgBA,GAAY,CAAA,GAE9CmB,KAAMJ,EAAgCN,EAAeF,IAIrDS,IACCb,GACIA,EAAea,EAASI,QACzBJ,EAASI,OAAS,KAAOJ,EAASI,QAAU,MAEhDd,GAAW,EACXL,GAAWA,EAAQ,CAAEe,aACrB9gB,EAAOghB,OAAOF,EAASI,SAEvBlB,GAAaA,EAAU,CAAEc,aAE3B,MAAOlY,GACPwX,GAAW,EACXL,GAAWA,EAAQ,CAAEnX,YAtDrBjE,CAyDHjE,GAEC0f,GAAY5a,EAAMb,UACpBa,EAAMb,QAAQgQ,UAAUC,MAAM5J,KAAK,CACjCsJ,oBAAoB,IAEtB9O,EAAMb,QAAQqU,SAAS,cAAe,CACpChZ,WASN,OAJAoE,EAAMkB,UAAU,KACdoa,GAAW,IACV,IAEIF,EACLpb,EAAA+c,cAAA/c,EAAAgd,SAAA,KACG5B,EAAO,CACNW,YAIJ/b,EAAA+c,cAAA,OAAA,CACEE,WAAY5B,EACZ1V,OAAQA,EACRoS,OAAQA,EACR2D,QAASA,EACTH,SAAUQ,KACND,GAEHN,EAGP,iBV/DEpa,IAEA,MAAMoa,SAAEA,KAAane,GAAS+D,EAC9B,OACEpB,EAAA+c,cAAChd,EAAgBmd,SAAQ,CAACphB,MAAOuB,GAC9Bme,8F4DRD,SAOJpa,GAOA,MAAMC,EAAUlB,KACVI,QACJA,EAAUc,EAAQd,QAAO5D,KACzBA,EAAIwgB,QACJA,EAAU,KAAIvZ,iBACdA,EAAgBM,MAChBA,GACE9C,GACG8H,EAAQkU,GAAapd,EAAMyB,SAASlB,EAAQgY,eAAe5b,IAC5D0gB,EAAMrd,EAAM4B,OAChBrB,EAAQgY,eAAe5b,GAAMsG,IAAIkW,KAE7BmE,EAAYtd,EAAM4B,OAAOsH,GACzBqU,EAAQvd,EAAM4B,OAAOjF,GACrB6gB,EAAYxd,EAAM4B,QAAO,GAE/B2b,EAAMjb,QAAU3F,EAChB2gB,EAAUhb,QAAU4G,EACpB3I,EAAQqC,OAAOkB,MAAMd,IAAIrG,GAEzBuH,GACG3D,EAA2D0D,SAC1DtH,EACAuH,GAGJlD,EACE,IACET,EAAQgQ,UAAUzM,MAAMgD,UAAU,CAChCF,KAAM,EACJpD,SACA7G,KAAM8gB,MAKN,GAAIA,IAAmBF,EAAMjb,UAAYmb,EAAgB,CACvD,MAAM9G,EAAc9X,EAAI2E,EAAQ+Z,EAAMjb,SAClCnG,MAAMC,QAAQua,KAChByG,EAAUzG,GACV0G,EAAI/a,QAAUqU,EAAY1T,IAAIkW,SAInCnS,YACL,CAACzG,IAGH,MAAMmd,EAAe1d,EAAM0E,YAMvBiZ,IAEAH,EAAUlb,SAAU,EACpB/B,EAAQuX,eAAenb,EAAMghB,IAE/B,CAACpd,EAAS5D,IAqRZ,OA5GAqD,EAAMkB,UAAU,KAQd,GAPAX,EAAQmF,OAAOC,QAAS,EAExBiG,GAAUjP,EAAM4D,EAAQqC,SACtBrC,EAAQgQ,UAAUC,MAAM5J,KAAK,IACxBrG,EAAQmB,aAIb8b,EAAUlb,WACRyI,GAAmBxK,EAAQgF,SAASyF,MAAMC,YAC1C1K,EAAQmB,WAAWsO,eACpBjF,GAAmBxK,EAAQgF,SAASmK,gBAAgBzE,WAErD,GAAI1K,EAAQgF,SAASqL,SACnBrQ,EAAQsQ,WAAW,CAAClU,IAAO8b,KAAMxZ,IAC/B,MAAMuF,EAAQ3F,EAAII,EAAOmD,OAAQzF,GAC3BihB,EAAgB/e,EAAI0B,EAAQmB,WAAWU,OAAQzF,IAGnDihB,GACMpZ,GAASoZ,EAAchiB,MACxB4I,IACEoZ,EAAchiB,OAAS4I,EAAM5I,MAC5BgiB,EAAcxY,UAAYZ,EAAMY,SACpCZ,GAASA,EAAM5I,QAEnB4I,EACIpF,EAAImB,EAAQmB,WAAWU,OAAQzF,EAAM6H,GACrCiE,GAAMlI,EAAQmB,WAAWU,OAAQzF,GACrC4D,EAAQgQ,UAAUC,MAAM5J,KAAK,CAC3BxE,OAAQ7B,EAAQmB,WAAWU,gBAI5B,CACL,MAAM0C,EAAejG,EAAI0B,EAAQwE,QAASpI,IAExCmI,IACAA,EAAME,IAEJ+F,GAAmBxK,EAAQgF,SAASmK,gBAAgBzE,YACpDF,GAAmBxK,EAAQgF,SAASyF,MAAMC,YAG5CgC,GACEnI,EACAvE,EAAQqC,OAAOtB,SACff,EAAQkD,YACRlD,EAAQgF,SAASmL,eAAiB7Q,EAClCU,EAAQgF,SAAS6H,2BACjB,GACAqL,KACCjU,IACEsD,EAActD,IACfjE,EAAQgQ,UAAUC,MAAM5J,KAAK,CAC3BxE,OAAQsK,GACNnM,EAAQmB,WAAWU,OACnBoC,EACA7H,MAQd4D,EAAQgQ,UAAUC,MAAM5J,KAAK,CAC3BjK,OACA6G,OAAQpG,EAAYmD,EAAQkD,eAG9BlD,EAAQqC,OAAOqC,OACbgH,GAAsB1L,EAAQwE,QAAS,CAACH,EAAK5G,KAC3C,GACEuC,EAAQqC,OAAOqC,OACfjH,EAAIgO,WAAWzL,EAAQqC,OAAOqC,QAC9BL,EAAIK,MAGJ,OADAL,EAAIK,QACG,IAKb1E,EAAQqC,OAAOqC,MAAQ,GAEvB1E,EAAQiC,YACRgb,EAAUlb,SAAU,GACnB,CAAC4G,EAAQvM,EAAM4D,IAElBP,EAAMkB,UAAU,MACbrC,EAAI0B,EAAQkD,YAAa9G,IAAS4D,EAAQuX,eAAenb,GAEnD,KAQL4D,EAAQgF,SAAS3B,kBAAoBA,EACjCrD,EAAQqF,WAAWjJ,GARD,EAACA,EAAyBb,KAC9C,MAAMgJ,EAAejG,EAAI0B,EAAQwE,QAASpI,GACtCmI,GAASA,EAAME,KACjBF,EAAME,GAAGS,MAAQ3J,IAMjB0J,CAAc7I,GAAM,KAEzB,CAACA,EAAM4D,EAAS4c,EAASvZ,IAErB,CACLia,KAAM7d,EAAM0E,YAlMD,CAACuW,EAAgBC,KAC5B,MAAMyC,EAA0Bpd,EAAQgY,eAAe5b,GACvDqe,GAAY2C,EAAyB1C,EAAQC,GAC7CF,GAAYqC,EAAI/a,QAAS2Y,EAAQC,GACjCwC,EAAaC,GACbP,EAAUO,GACVpd,EAAQuX,eACNnb,EACAghB,EACA3C,GACA,CACE7C,KAAM8C,EACN7C,KAAM8C,IAER,IAoL4B,CAACwC,EAAc/gB,EAAM4D,IACnDud,KAAM9d,EAAM0E,YAjLD,CAACsM,EAAcqJ,KAC1B,MAAMsD,EAA0Bpd,EAAQgY,eAAe5b,GACvDyd,GAAYuD,EAAyB3M,EAAMqJ,GAC3CD,GAAYiD,EAAI/a,QAAS0O,EAAMqJ,GAC/BqD,EAAaC,GACbP,EAAUO,GACVpd,EAAQuX,eACNnb,EACAghB,EACAvD,GACA,CACEjC,KAAMnH,EACNoH,KAAMiC,IAER,IAmK4B,CAACqD,EAAc/gB,EAAM4D,IACnDwd,QAAS/d,EAAM0E,YA7PD,CACd5I,EAGA4N,KAEA,MAAMsU,EAAexX,EAAsBpJ,EAAYtB,IACjD6hB,EAA0BpD,GAC9Bha,EAAQgY,eAAe5b,GACvBqhB,GAEFzd,EAAQqC,OAAOqC,MAAQ6U,GAAkBnd,EAAM,EAAG+M,GAClD2T,EAAI/a,QAAUiY,GAAU8C,EAAI/a,QAAS0b,EAAa/a,IAAIkW,KACtDuE,EAAaC,GACbP,EAAUO,GACVpd,EAAQuX,eAAenb,EAAMghB,EAAyBpD,GAAW,CAC/DpC,KAAM+B,GAAepe,MA6Oa,CAAC4hB,EAAc/gB,EAAM4D,IACzDic,OAAQxc,EAAM0E,YAtRD,CACb5I,EAGA4N,KAEA,MAAMuU,EAAczX,EAAsBpJ,EAAYtB,IAChD6hB,EAA0B1D,GAC9B1Z,EAAQgY,eAAe5b,GACvBshB,GAEF1d,EAAQqC,OAAOqC,MAAQ6U,GACrBnd,EACAghB,EAAwBpe,OAAS,EACjCmK,GAEF2T,EAAI/a,QAAU2X,GAASoD,EAAI/a,QAAS2b,EAAYhb,IAAIkW,KACpDuE,EAAaC,GACbP,EAAUO,GACVpd,EAAQuX,eAAenb,EAAMghB,EAAyB1D,GAAU,CAC9D9B,KAAM+B,GAAepe,MAkQW,CAAC4hB,EAAc/gB,EAAM4D,IACvD2d,OAAQle,EAAM0E,YA3OArF,IACd,MAAMse,EAEAnD,GAAcja,EAAQgY,eAAe5b,GAAO0C,GAClDge,EAAI/a,QAAUkY,GAAc6C,EAAI/a,QAASjD,GACzCqe,EAAaC,GACbP,EAAUO,IACTxhB,MAAMC,QAAQyC,EAAI0B,EAAQwE,QAASpI,KAClCyC,EAAImB,EAAQwE,QAASpI,OAAM0B,GAC7BkC,EAAQuX,eAAenb,EAAMghB,EAAyBnD,GAAe,CACnErC,KAAM9Y,KAiO0B,CAACqe,EAAc/gB,EAAM4D,IACvD4Z,OAAQna,EAAM0E,YA9ND,CACbrF,EACAvD,EAGA4N,KAEA,MAAMyU,EAAc3X,EAAsBpJ,EAAYtB,IAChD6hB,EAA0BS,GAC9B7d,EAAQgY,eAAe5b,GACvB0C,EACA8e,GAEF5d,EAAQqC,OAAOqC,MAAQ6U,GAAkBnd,EAAM0C,EAAOqK,GACtD2T,EAAI/a,QAAU8b,GAASf,EAAI/a,QAASjD,EAAO8e,EAAYlb,IAAIkW,KAC3DuE,EAAaC,GACbP,EAAUO,GACVpd,EAAQuX,eAAenb,EAAMghB,EAAyBS,GAAU,CAC9DjG,KAAM9Y,EACN+Y,KAAM8B,GAAepe,MA2MW,CAAC4hB,EAAc/gB,EAAM4D,IACvD8d,OAAQre,EAAM0E,YApKD,CACbrF,EACAvD,KAEA,MAAMwH,EAAclG,EAAYtB,GAC1B6hB,EAA0BxC,GAC9B5a,EAAQgY,eAEN5b,GACF0C,EACAiE,GAEF+Z,EAAI/a,QAAU,IAAIqb,GAAyB1a,IAAI,CAACqb,EAAM5D,IACnD4D,GAAQ5D,IAAMrb,EAAuBge,EAAI/a,QAAQoY,GAA3BvB,MAEzBuE,EAAaC,GACbP,EAAU,IAAIO,IACdpd,EAAQuX,eACNnb,EACAghB,EACAxC,GACA,CACEhD,KAAM9Y,EACN+Y,KAAM9U,IAER,GACA,IA0IgC,CAACoa,EAAc/gB,EAAM4D,IACvD5B,QAASqB,EAAM0E,YAtIf5I,IAIA,MAAM6hB,EAA0BnX,EAAsBpJ,EAAYtB,IAClEuhB,EAAI/a,QAAUqb,EAAwB1a,IAAIkW,IAC1CuE,EAAa,IAAIC,IACjBP,EAAU,IAAIO,IACdpd,EAAQuX,eACNnb,EACA,IAAIghB,GACAtgB,GAAeA,EACnB,IACA,GACA,IAwHkC,CAACqgB,EAAc/gB,EAAM4D,IACzD2I,OAAQlJ,EAAMyC,QACZ,IACEyG,EAAOjG,IAAI,CAAC6B,EAAOzF,KAAK,IACnByF,EACHqY,CAACA,GAAUE,EAAI/a,QAAQjD,IAAU8Z,QAErC,CAACjQ,EAAQiU,IAGf,YCrZM,SAKJ/b,EAAkE,IAElE,MAAMmd,EAAeve,EAAM4B,YAEzBvD,GACImgB,EAAUxe,EAAM4B,YAA4BvD,IAC3CiC,EAAWkB,GAAmBxB,EAAMyB,SAAkC,CAC3EI,SAAS,EACTK,cAAc,EACdJ,UAAWkG,EAAW5G,EAAMV,eAC5BsP,aAAa,EACbC,cAAc,EACdC,oBAAoB,EACpB/N,SAAS,EACT2N,YAAa,EACb/N,YAAa,CAAA,EACbC,cAAe,CAAA,EACfC,iBAAkB,CAAA,EAClBG,OAAQhB,EAAMgB,QAAU,CAAA,EACxBd,SAAUF,EAAME,WAAY,EAC5ByO,SAAS,EACTrP,cAAesH,EAAW5G,EAAMV,oBAC5BrC,EACA+C,EAAMV,gBAGZ,IAAK6d,EAAajc,QAChB,GAAIlB,EAAM8X,YACRqF,EAAajc,QAAU,IAClBlB,EAAM8X,YACT5Y,aAGEc,EAAMV,gBAAkBsH,EAAW5G,EAAMV,gBAC3CU,EAAM8X,YAAY1B,MAAMpW,EAAMV,cAAeU,EAAMsX,kBAEhD,CACL,MAAMQ,YAAEA,KAAgB4C,GAASlM,GAAkBxO,GAEnDmd,EAAajc,QAAU,IAClBwZ,EACHxb,aAKN,MAAMC,EAAUge,EAAajc,QAAQ/B,QAwFrC,OAvFAA,EAAQgF,SAAWnE,EAEnBJ,EAA0B,KACxB,MAAMyd,EAAMle,EAAQ8B,WAAW,CAC7B/B,UAAWC,EAAQQ,gBACnBwB,SAAU,IAAMf,EAAgB,IAAKjB,EAAQmB,aAC7C2T,cAAc,IAUhB,OAPA7T,EAAiBnE,IAAI,IAChBA,EACH0S,SAAS,KAGXxP,EAAQmB,WAAWqO,SAAU,EAEtB0O,GACN,CAACle,IAEJP,EAAMkB,UACJ,IAAMX,EAAQoY,aAAavX,EAAME,UACjC,CAACf,EAASa,EAAME,WAGlBtB,EAAMkB,UAAU,KACVE,EAAM4J,OACRzK,EAAQgF,SAASyF,KAAO5J,EAAM4J,MAE5B5J,EAAMsO,iBACRnP,EAAQgF,SAASmK,eAAiBtO,EAAMsO,iBAEzC,CAACnP,EAASa,EAAM4J,KAAM5J,EAAMsO,iBAE/B1P,EAAMkB,UAAU,KACVE,EAAMgB,SACR7B,EAAQ+X,WAAWlX,EAAMgB,QACzB7B,EAAQ4V,gBAET,CAAC5V,EAASa,EAAMgB,SAEnBpC,EAAMkB,UAAU,KACdE,EAAMwC,kBACJrD,EAAQgQ,UAAUC,MAAM5J,KAAK,CAC3BpD,OAAQjD,EAAQgD,eAEnB,CAAChD,EAASa,EAAMwC,mBAEnB5D,EAAMkB,UAAU,KACd,GAAIX,EAAQQ,gBAAgBc,QAAS,CACnC,MAAMA,EAAUtB,EAAQqR,YACpB/P,IAAYvB,EAAUuB,SACxBtB,EAAQgQ,UAAUC,MAAM5J,KAAK,CAC3B/E,cAIL,CAACtB,EAASD,EAAUuB,UAEvB7B,EAAMkB,UAAU,KACVE,EAAMoC,SAAW2D,EAAU/F,EAAMoC,OAAQgb,EAAQlc,UACnD/B,EAAQsW,OAAOzV,EAAMoC,OAAQ,CAC3BiU,eAAe,KACZlX,EAAQgF,SAASmT,eAEtB8F,EAAQlc,QAAUlB,EAAMoC,OACxBhC,EAAiBgP,IAAK,IAAWA,MAEjCjQ,EAAQiY,uBAET,CAACjY,EAASa,EAAMoC,SAEnBxD,EAAMkB,UAAU,KACTX,EAAQmF,OAAOD,QAClBlF,EAAQiC,YACRjC,EAAQmF,OAAOD,OAAQ,GAGrBlF,EAAQmF,OAAO3C,QACjBxC,EAAQmF,OAAO3C,OAAQ,EACvBxC,EAAQgQ,UAAUC,MAAM5J,KAAK,IAAKrG,EAAQmB,cAG5CnB,EAAQmD,qBAGV6a,EAAajc,QAAQhC,UAAYD,EAAkBC,EAAWC,GAEvDge,EAAajc,OACtB"}