{"name": "ai-ecommerce", "version": "1.0.0", "description": "AI-driven e-commerce platform built with MERN stack", "main": "index.js", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\" \"npm run ai\"", "server": "cd server && npm run dev", "client": "cd client/ecommerce && npm run dev", "ai": "cd ai-service && python app.py", "install-all": "npm install && cd server && npm install && cd ../client/ecommerce && npm install && cd ../../ai-service && pip install -r requirements.txt", "build": "cd client/ecommerce && npm run build", "start": "cd server && npm start", "kill-ports": "npx kill-port 3000 5000 5173 8000"}, "keywords": ["ecommerce", "ai", "mern", "react", "nodejs", "mongodb", "machine-learning"], "author": "Your Name", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2", "kill-port": "^2.0.1"}}