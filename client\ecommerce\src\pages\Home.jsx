import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { ArrowRight, Sparkles, TrendingUp, Star } from 'lucide-react';
import { productsAPI, aiAPI } from '../services/api';
import { useApp } from '../context/AppContext';
import Button from '../components/ui/Button';
import ProductCard from '../components/product/ProductCard';
import { ProductCardSkeleton } from '../components/ui/Loading';

const Home = () => {
  const [featuredProducts, setFeaturedProducts] = useState([]);
  const [aiRecommendations, setAiRecommendations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [aiLoading, setAiLoading] = useState(true);
  const { addToCart, addToWishlist, wishlist, user } = useApp();

  useEffect(() => {
    loadFeaturedProducts();
    loadAiRecommendations();
  }, [user]);

  const loadFeaturedProducts = async () => {
    try {
      const response = await productsAPI.getFeaturedProducts(8);
      setFeaturedProducts(response.data);
    } catch (error) {
      console.error('Error loading featured products:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadAiRecommendations = async () => {
    try {
      setAiLoading(true);
      const response = await aiAPI.getRecommendations({
        limit: 6,
        user_id: user?.id
      });

      if (response.success && response.data.recommendations) {
        setAiRecommendations(response.data.recommendations);
      }
    } catch (error) {
      console.error('Error loading AI recommendations:', error);
    } finally {
      setAiLoading(false);
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  return (
    <div className="space-y-16">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-800 text-white overflow-hidden min-h-screen flex items-center">
        {/* Background decorations */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-br from-blue-400 to-purple-600 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob"></div>
          <div className="absolute top-1/3 right-1/4 w-96 h-96 bg-gradient-to-br from-purple-400 to-pink-600 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-2000"></div>
          <div className="absolute bottom-1/4 left-1/3 w-96 h-96 bg-gradient-to-br from-yellow-400 to-orange-600 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-4000"></div>
        </div>

        <div className="absolute inset-0 bg-gradient-to-br from-black/20 via-transparent to-black/20"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-32 z-10">
          <motion.div
            className="text-center"
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            <motion.div variants={itemVariants} className="flex justify-center mb-8">
              <div className="flex items-center space-x-3 bg-white/20 backdrop-blur-md rounded-full px-6 py-3 border border-white/30">
                <Sparkles className="h-6 w-6 text-yellow-300" />
                <span className="text-lg font-semibold">AI-Powered Smart Shopping</span>
              </div>
            </motion.div>

            <motion.h1
              variants={itemVariants}
              className="text-5xl md:text-7xl lg:text-8xl font-bold mb-8 leading-tight"
            >
              Discover Amazing
              <br />
              <span className="bg-gradient-to-r from-yellow-300 via-orange-300 to-pink-300 bg-clip-text text-transparent">
                Products
              </span>
              <br />
              <span className="text-4xl md:text-5xl lg:text-6xl text-blue-100">Tailored for You</span>
            </motion.h1>
            
            <motion.p
              variants={itemVariants}
              className="text-xl md:text-2xl lg:text-3xl mb-12 max-w-4xl mx-auto opacity-90 leading-relaxed"
            >
              Experience the future of e-commerce with AI-powered recommendations,
              smart search, and personalized shopping experiences that understand your style.
            </motion.p>
            
            <motion.div
              variants={itemVariants}
              className="flex flex-col sm:flex-row gap-6 justify-center"
            >
              <Link to="/products">
                <Button
                  size="lg"
                  className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-4 text-lg font-bold shadow-2xl hover:shadow-3xl transform hover:scale-105"
                >
                  Shop Now
                  <ArrowRight className="ml-3 h-6 w-6" />
                </Button>
              </Link>
              <Button
                variant="outline"
                size="lg"
                className="border-2 border-white text-white hover:bg-white hover:text-blue-600 px-8 py-4 text-lg font-bold backdrop-blur-md bg-white/10"
              >
                Learn More
              </Button>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Features Section */}
      <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <motion.div
          className="text-center mb-12"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          <motion.h2 variants={itemVariants} className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent mb-6">
            Why Choose AI Commerce?
          </motion.h2>
          <motion.p variants={itemVariants} className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            We combine cutting-edge AI technology with exceptional customer service
            to deliver a shopping experience like no other.
          </motion.p>
        </motion.div>

        <motion.div
          className="grid grid-cols-1 md:grid-cols-3 gap-8"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          {[
            {
              icon: Sparkles,
              title: 'AI Recommendations',
              description: 'Get personalized product suggestions based on your preferences and browsing history.'
            },
            {
              icon: TrendingUp,
              title: 'Smart Analytics',
              description: 'Advanced sentiment analysis helps you make informed decisions with real customer insights.'
            },
            {
              icon: Star,
              title: 'Quality Guaranteed',
              description: 'Every product is carefully curated and backed by our satisfaction guarantee.'
            }
          ].map((feature, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className="text-center p-8 rounded-3xl bg-white/70 backdrop-blur-lg border border-white/20 hover:shadow-2xl transition-all duration-300 card-hover group"
            >
              <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl mb-6 group-hover:scale-110 transition-transform duration-300">
                <feature.icon className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">{feature.title}</h3>
              <p className="text-gray-600 text-lg leading-relaxed">{feature.description}</p>
            </motion.div>
          ))}
        </motion.div>
      </section>

      {/* Featured Products Section */}
      <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="flex items-center justify-between mb-8"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
        >
          <div>
            <h2 className="text-3xl font-bold text-gray-900 mb-2">Featured Products</h2>
            <p className="text-gray-600">Discover our most popular and trending items</p>
          </div>
          <Link to="/products">
            <Button variant="outline">
              View All
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </Link>
        </motion.div>

        {loading ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(8)].map((_, index) => (
              <ProductCardSkeleton key={index} />
            ))}
          </div>
        ) : (
          <motion.div
            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6"
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
          >
            {featuredProducts.map((product) => (
              <motion.div key={product._id} variants={itemVariants}>
                <ProductCard
                  product={product}
                  onAddToCart={addToCart}
                  onAddToWishlist={addToWishlist}
                  isInWishlist={wishlist.some(item => item._id === product._id)}
                />
              </motion.div>
            ))}
          </motion.div>
        )}
      </section>

      {/* AI Recommendations Section */}
      {user && (
        <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="flex items-center justify-between mb-8"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
          >
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-2">
                <Sparkles className="inline h-8 w-8 text-blue-600 mr-2" />
                Recommended for You
              </h2>
              <p className="text-gray-600">AI-powered recommendations based on your preferences</p>
            </div>
          </motion.div>

          {aiLoading ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, index) => (
                <ProductCardSkeleton key={index} />
              ))}
            </div>
          ) : aiRecommendations.length > 0 ? (
            <motion.div
              className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6"
              variants={containerVariants}
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
            >
              {aiRecommendations.map((product) => (
                <motion.div key={product.product_id} variants={itemVariants}>
                  <ProductCard
                    product={{
                      _id: product.product_id,
                      name: product.name,
                      price: product.price,
                      rating: { average: product.rating, count: 0 },
                      category: product.category,
                      images: [{ url: '/placeholder-product.jpg' }],
                      stock: { quantity: 10 }
                    }}
                    onAddToCart={addToCart}
                    onAddToWishlist={addToWishlist}
                    isInWishlist={wishlist.some(item => item._id === product.product_id)}
                  />
                </motion.div>
              ))}
            </motion.div>
          ) : (
            <div className="text-center py-12">
              <Sparkles className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <p className="text-gray-500 text-lg">
                Start shopping to get personalized recommendations!
              </p>
            </div>
          )}
        </section>
      )}

      {/* CTA Section */}
      <section className="bg-gray-900 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <motion.div
            className="text-center"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold mb-4">Ready to Start Shopping?</h2>
            <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
              Join thousands of satisfied customers who trust AI Commerce for their shopping needs.
            </p>
            <Link to="/products">
              <Button size="lg" className="bg-blue-600 hover:bg-blue-700">
                Explore Products
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default Home;
