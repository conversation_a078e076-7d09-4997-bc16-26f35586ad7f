import { motion } from 'framer-motion';
import { forwardRef } from 'react';

const Card = forwardRef(({
  children,
  className = '',
  hover = true,
  padding = 'md',
  shadow = 'md',
  ...props
}, ref) => {
  const baseClasses = 'bg-white rounded-lg border border-gray-200 transition-all duration-200';
  
  const paddings = {
    none: '',
    sm: 'p-3',
    md: 'p-4',
    lg: 'p-6',
    xl: 'p-8'
  };
  
  const shadowValues = {
    none: '0 0 0 0 rgba(0, 0, 0, 0)',
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
  };

  const hoverShadowValues = {
    none: '0 0 0 0 rgba(0, 0, 0, 0)',
    sm: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
    md: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    lg: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
    xl: '0 25px 50px -12px rgba(0, 0, 0, 0.25)'
  };

  const classes = `${baseClasses} ${paddings[padding]} ${className}`;

  const cardVariants = {
    initial: { y: 0 },
    hover: { y: -2 }
  };
  
  return (
    <motion.div
      ref={ref}
      className={classes}
      style={{
        boxShadow: shadowValues[shadow]
      }}
      variants={hover ? cardVariants : undefined}
      initial="initial"
      whileHover={hover ? {
        y: -2,
        boxShadow: hoverShadowValues[shadow]
      } : undefined}
      transition={{ duration: 0.2 }}
      {...props}
    >
      {children}
    </motion.div>
  );
});

Card.displayName = 'Card';

export default Card;
