!function(e,r){"object"==typeof exports&&"undefined"!=typeof module?r(exports,require("@hookform/resolvers"),require("ajv"),require("ajv-errors"),require("react-hook-form")):"function"==typeof define&&define.amd?define(["exports","@hookform/resolvers","ajv","ajv-errors","react-hook-form"],r):r((e||self).hookformResolversAjv={},e.hookformResolvers,e.ajv,e.ajvErrors,e.ReactHookForm)}(this,function(e,r,o,s,a){function t(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var n=/*#__PURE__*/t(o),i=/*#__PURE__*/t(s),f=function(e,r){for(var o={},s=function(e){"required"===e.keyword&&(e.instancePath+="/"+e.params.missingProperty);var s=e.instancePath.substring(1).replace(/\//g,".");if(o[s]||(o[s]={message:e.message,type:e.keyword}),r){var t=o[s].types,n=t&&t[e.keyword];o[s]=a.appendErrors(s,r,o,e.keyword,n?[].concat(n,e.message||""):e.message)}},t=function(){var r=e[n];"errorMessage"===r.keyword?r.params.errors.forEach(function(e){e.message=r.message,s(e)}):s(r)},n=0;n<e.length;n+=1)t();return o};e.ajvResolver=function(e,o,s){return void 0===s&&(s={}),function(a,t,l){try{var u=new n.default(Object.assign({},{allErrors:!0,validateSchema:!0},o));i.default(u);var c=u.compile(Object.assign({$async:s&&"async"===s.mode},e)),d=c(a);return l.shouldUseNativeValidation&&r.validateFieldsNatively({},l),Promise.resolve(d?{values:a,errors:{}}:{values:{},errors:r.toNestErrors(f(c.errors,!l.shouldUseNativeValidation&&"all"===l.criteriaMode),l)})}catch(e){return Promise.reject(e)}}}});
//# sourceMappingURL=ajv.umd.js.map
