var e=require("@hookform/resolvers"),r=require("ajv"),a=require("ajv-errors"),s=require("react-hook-form");function o(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var t=/*#__PURE__*/o(r),i=/*#__PURE__*/o(a),n=function(e,r){for(var a={},o=function(e){"required"===e.keyword&&(e.instancePath+="/"+e.params.missingProperty);var o=e.instancePath.substring(1).replace(/\//g,".");if(a[o]||(a[o]={message:e.message,type:e.keyword}),r){var t=a[o].types,i=t&&t[e.keyword];a[o]=s.appendErrors(o,r,a,e.keyword,i?[].concat(i,e.message||""):e.message)}},t=function(){var r=e[i];"errorMessage"===r.keyword?r.params.errors.forEach(function(e){e.message=r.message,o(e)}):o(r)},i=0;i<e.length;i+=1)t();return a};exports.ajvResolver=function(r,a,s){return void 0===s&&(s={}),function(o,u,c){try{var l=new t.default(Object.assign({},{allErrors:!0,validateSchema:!0},a));i.default(l);var v=l.compile(Object.assign({$async:s&&"async"===s.mode},r)),d=v(o);return c.shouldUseNativeValidation&&e.validateFieldsNatively({},c),Promise.resolve(d?{values:o,errors:{}}:{values:{},errors:e.toNestErrors(n(v.errors,!c.shouldUseNativeValidation&&"all"===c.criteriaMode),c)})}catch(e){return Promise.reject(e)}}};
//# sourceMappingURL=ajv.js.map
