#!/bin/bash

echo "Starting AI E-Commerce Development Environment..."
echo

echo "Installing dependencies..."
echo

echo "[1/3] Installing root dependencies..."
npm install

echo "[2/3] Installing server dependencies..."
cd server
npm install
cd ..

echo "[3/3] Installing client dependencies..."
cd client/ecommerce
npm install
cd ../..

echo
echo "Dependencies installed successfully!"
echo

echo "Starting all services..."
echo

echo "Killing any existing processes on ports 5000, 5173, 8000..."
npx kill-port 5000 5173 8000 2>/dev/null || true
sleep 2

# Function to start services in background
start_service() {
    local name=$1
    local command=$2
    echo "Starting $name..."
    eval "$command" &
}

# Start AI service
start_service "AI Service" "cd ai-service && python app.py"
sleep 3

# Start backend server
start_service "Backend Server" "cd server && npm run dev"
sleep 3

# Start frontend client
start_service "Frontend Client" "cd client/ecommerce && npm run dev"

echo
echo "All services are starting..."
echo
echo "Frontend: http://localhost:5173"
echo "Backend: http://localhost:5000"
echo "AI Service: http://localhost:8000"
echo
echo "Press Ctrl+C to stop all services"

# Wait for user input
wait
