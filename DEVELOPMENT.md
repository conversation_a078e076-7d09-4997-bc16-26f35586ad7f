# AI E-Commerce Development Guide

## 🚀 Quick Start

### Prerequisites
- **Node.js** (v18 or higher)
- **Python** (v3.8 or higher)
- **MongoDB** (local installation or MongoDB Atlas)
- **Git**

### One-Command Setup

**Windows:**
```bash
start-dev.bat
```

**macOS/Linux:**
```bash
chmod +x start-dev.sh
./start-dev.sh
```

This will:
1. Install all dependencies
2. Start all three services (Frontend, Backend, AI Service)
3. Open the application in your browser

## 🏗️ Manual Setup

### 1. Install Dependencies

```bash
# Root dependencies
npm install

# Backend dependencies
cd server
npm install
cd ..

# Frontend dependencies
cd client/ecommerce
npm install
cd ../..

# AI service dependencies
cd ai-service
pip install -r requirements.txt
cd ..
```

### 2. Environment Configuration

Copy the example environment files and configure them:

```bash
# Backend
cp server/.env.example server/.env

# Frontend
cp client/ecommerce/.env.example client/ecommerce/.env

# AI Service
cp ai-service/.env.example ai-service/.env
```

### 3. Start Services

**Terminal 1 - AI Service:**
```bash
cd ai-service
python app.py
```

**Terminal 2 - Backend:**
```bash
cd server
npm run dev
```

**Terminal 3 - Frontend:**
```bash
cd client/ecommerce
npm run dev
```

## 🌐 Service URLs

- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:5000
- **AI Service**: http://localhost:8000
- **API Documentation**: http://localhost:5000/api/health

## 🧪 Testing the Application

### 1. Basic Flow Test
1. Open http://localhost:5173
2. Browse products on the home page
3. Register a new account
4. Add products to cart
5. View cart and proceed to checkout

### 2. AI Features Test
1. **Recommendations**: Login and check "Recommended for You" section
2. **Search**: Use the search functionality
3. **Sentiment Analysis**: Leave a product review (coming soon)

### 3. API Testing

Test the backend APIs using curl or Postman:

```bash
# Health check
curl http://localhost:5000/health

# Get products
curl http://localhost:5000/api/products

# AI recommendations
curl http://localhost:5000/api/ai/recommendations

# AI health check
curl http://localhost:8000/health
```

## 📁 Project Structure

```
AI_Ecommerce/
├── client/ecommerce/          # React frontend
│   ├── src/
│   │   ├── components/        # Reusable UI components
│   │   ├── pages/            # Page components
│   │   ├── services/         # API services
│   │   ├── context/          # React context
│   │   └── utils/            # Utility functions
│   └── package.json
├── server/                   # Express backend
│   ├── controllers/          # Route controllers
│   ├── models/              # MongoDB models
│   ├── routes/              # API routes
│   ├── middleware/          # Custom middleware
│   └── package.json
├── ai-service/              # Python AI service
│   ├── models/              # AI models
│   ├── utils/               # Helper functions
│   ├── app.py              # FastAPI application
│   └── requirements.txt
└── README.md
```

## 🔧 Development Commands

### Frontend (React + Vite)
```bash
cd client/ecommerce
npm run dev          # Start development server
npm run build        # Build for production
npm run preview      # Preview production build
npm run lint         # Run ESLint
```

### Backend (Express)
```bash
cd server
npm run dev          # Start with nodemon
npm start           # Start production server
npm test            # Run tests
```

### AI Service (FastAPI)
```bash
cd ai-service
python app.py       # Start development server
uvicorn app:app --reload  # Alternative start command
```

## 🐛 Troubleshooting

### Common Issues

1. **Port already in use (EADDRINUSE error)**
   - Run the cleanup script first:
     - Windows: `cleanup-ports.bat`
     - macOS/Linux: `chmod +x cleanup-ports.sh && ./cleanup-ports.sh`
   - Or manually kill processes: `npx kill-port 5173 5000 8000`
   - Change ports in environment files if needed

2. **MongoDB connection failed**
   - Ensure MongoDB is running locally
   - Check connection string in `.env` files
   - Use MongoDB Atlas for cloud database

3. **Python dependencies issues**
   - Create virtual environment: `python -m venv venv`
   - Activate: `venv\Scripts\activate` (Windows) or `source venv/bin/activate` (macOS/Linux)
   - Install dependencies: `pip install -r requirements.txt`

4. **AI service not responding**
   - Check if Python dependencies are installed
   - Verify AI service is running on port 8000
   - Check logs for NLTK download issues

### Logs and Debugging

- **Frontend**: Check browser console for errors
- **Backend**: Check terminal output and `server/logs/`
- **AI Service**: Check terminal output and `ai-service/ai_service.log`

## 🚀 Deployment

### Frontend (Vercel)
1. Connect GitHub repository to Vercel
2. Set environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

### Backend (Render/Railway)
1. Connect GitHub repository
2. Set environment variables
3. Configure build and start commands

### AI Service (Render/Railway)
1. Create Python service
2. Set environment variables
3. Configure requirements.txt and start command

### Database (MongoDB Atlas)
1. Create cluster on MongoDB Atlas
2. Update connection strings in environment files
3. Configure network access and database users

## 📚 Additional Resources

- [React Documentation](https://react.dev/)
- [Express.js Guide](https://expressjs.com/)
- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [MongoDB Documentation](https://docs.mongodb.com/)
- [Tailwind CSS](https://tailwindcss.com/)
- [Framer Motion](https://www.framer.com/motion/)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.
