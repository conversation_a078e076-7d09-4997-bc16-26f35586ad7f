import os
import logging
from motor.motor_asyncio import AsyncIOMotorClient

logger = logging.getLogger(__name__)

class Database:
    """Database connection manager for AI service"""

    def __init__(self):
        self.client = None
        self.database = None
        
    async def connect(self):
        """Connect to MongoDB"""
        try:
            mongodb_url = os.getenv("MONGODB_URI", "mongodb://localhost:27017")
            database_name = os.getenv("DATABASE_NAME", "ai_ecommerce")
            
            self.client = AsyncIOMotorClient(mongodb_url)
            self.database = self.client[database_name]
            
            # Test connection
            await self.client.admin.command('ping')
            logger.info(f"Connected to MongoDB: {database_name}")
            
        except Exception as e:
            logger.error(f"Failed to connect to MongoDB: {str(e)}")
            raise
    
    async def disconnect(self):
        """Disconnect from MongoDB"""
        if self.client:
            self.client.close()
            logger.info("Disconnected from MongoDB")
    
    def get_collection(self, collection_name: str):
        """Get a collection from the database"""
        if not self.database:
            raise Exception("Database not connected")
        return self.database[collection_name]

# Global database instance
db_instance = Database()

async def get_database():
    """Get database instance"""
    if not db_instance.client:
        await db_instance.connect()
    return db_instance

async def close_database():
    """Close database connection"""
    await db_instance.disconnect()
