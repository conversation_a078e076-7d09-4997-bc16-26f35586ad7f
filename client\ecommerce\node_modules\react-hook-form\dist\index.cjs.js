"use strict";var e=require("react");function t(e){var t=Object.create(null);return e&&Object.keys(e).forEach(function(r){if("default"!==r){var s=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,s.get?s:{enumerable:!0,get:function(){return e[r]}})}}),t.default=e,Object.freeze(t)}var r=t(e),s=e=>"checkbox"===e.type,a=e=>e instanceof Date,i=e=>null==e;const n=e=>"object"==typeof e;var o=e=>!i(e)&&!Array.isArray(e)&&n(e)&&!a(e),l=e=>o(e)&&e.target?s(e.target)?e.target.checked:e.target.value:e,u=(e,t)=>e.has((e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e)(t)),d="undefined"!=typeof window&&void 0!==window.HTMLElement&&"undefined"!=typeof document;function c(e){let t;const r=Array.isArray(e),s="undefined"!=typeof FileList&&e instanceof FileList;if(e instanceof Date)t=new Date(e);else{if(d&&(e instanceof Blob||s)||!r&&!o(e))return e;if(t=r?[]:{},r||(e=>{const t=e.constructor&&e.constructor.prototype;return o(t)&&t.hasOwnProperty("isPrototypeOf")})(e))for(const r in e)e.hasOwnProperty(r)&&(t[r]=c(e[r]));else t=e}return t}var f=e=>/^\w*$/.test(e),m=e=>void 0===e,y=e=>Array.isArray(e)?e.filter(Boolean):[],g=e=>y(e.replace(/["|']|\]/g,"").split(/\.|\[/)),b=(e,t,r)=>{if(!t||!o(e))return r;const s=(f(t)?[t]:g(t)).reduce((e,t)=>i(e)?e:e[t],e);return m(s)||s===e?m(e[t])?r:e[t]:s},p=e=>"boolean"==typeof e,_=(e,t,r)=>{let s=-1;const a=f(t)?[t]:g(t),i=a.length,n=i-1;for(;++s<i;){const t=a[s];let i=r;if(s!==n){const r=e[t];i=o(r)||Array.isArray(r)?r:isNaN(+a[s+1])?{}:[]}if("__proto__"===t||"constructor"===t||"prototype"===t)return;e[t]=i,e=e[t]}};const h="blur",v="focusout",V="change",x="onBlur",F="onChange",A="onSubmit",S="onTouched",w="all",k="max",D="min",C="maxLength",E="minLength",O="pattern",j="required",M="validate",U=e.createContext(null);U.displayName="HookFormContext";const T=()=>e.useContext(U);var N=(e,t,r,s=!0)=>{const a={defaultValues:t._defaultValues};for(const i in e)Object.defineProperty(a,i,{get:()=>{const a=i;return t._proxyFormState[a]!==w&&(t._proxyFormState[a]=!s||w),r&&(r[a]=!0),e[a]}});return a};const R="undefined"!=typeof window?r.useLayoutEffect:r.useEffect;function B(t){const r=T(),{control:s=r.control,disabled:a,name:i,exact:n}=t||{},[o,l]=e.useState(s._formState),u=e.useRef({isDirty:!1,isLoading:!1,dirtyFields:!1,touchedFields:!1,validatingFields:!1,isValidating:!1,isValid:!1,errors:!1});return R(()=>s._subscribe({name:i,formState:u.current,exact:n,callback:e=>{!a&&l({...s._formState,...e})}}),[i,a,n]),e.useEffect(()=>{u.current.isValid&&s._setValid(!0)},[s]),e.useMemo(()=>N(o,s,u.current,!1),[o,s])}var L=e=>"string"==typeof e,P=(e,t,r,s,a)=>L(e)?(s&&t.watch.add(e),b(r,e,a)):Array.isArray(e)?e.map(e=>(s&&t.watch.add(e),b(r,e))):(s&&(t.watchAll=!0),r);function I(t){const r=T(),{control:s=r.control,name:a,defaultValue:i,disabled:n,exact:o}=t||{},l=e.useRef(i),[u,d]=e.useState(s._getWatch(a,l.current));return R(()=>s._subscribe({name:a,formState:{values:!0},exact:o,callback:e=>!n&&d(P(a,s._names,e.values||s._formValues,!1,l.current))}),[a,s,n,o]),e.useEffect(()=>s._removeUnmounted()),u}function W(t){const r=T(),{name:s,disabled:a,control:i=r.control,shouldUnregister:n}=t,o=u(i._names.array,s),d=I({control:i,name:s,defaultValue:b(i._formValues,s,b(i._defaultValues,s,t.defaultValue)),exact:!0}),f=B({control:i,name:s,exact:!0}),y=e.useRef(t),g=e.useRef(i.register(s,{...t.rules,value:d,...p(t.disabled)?{disabled:t.disabled}:{}})),v=e.useMemo(()=>Object.defineProperties({},{invalid:{enumerable:!0,get:()=>!!b(f.errors,s)},isDirty:{enumerable:!0,get:()=>!!b(f.dirtyFields,s)},isTouched:{enumerable:!0,get:()=>!!b(f.touchedFields,s)},isValidating:{enumerable:!0,get:()=>!!b(f.validatingFields,s)},error:{enumerable:!0,get:()=>b(f.errors,s)}}),[f,s]),x=e.useCallback(e=>g.current.onChange({target:{value:l(e),name:s},type:V}),[s]),F=e.useCallback(()=>g.current.onBlur({target:{value:b(i._formValues,s),name:s},type:h}),[s,i._formValues]),A=e.useCallback(e=>{const t=b(i._fields,s);t&&e&&(t._f.ref={focus:()=>e.focus&&e.focus(),select:()=>e.select&&e.select(),setCustomValidity:t=>e.setCustomValidity(t),reportValidity:()=>e.reportValidity()})},[i._fields,s]),S=e.useMemo(()=>({name:s,value:d,...p(a)||f.disabled?{disabled:f.disabled||a}:{},onChange:x,onBlur:F,ref:A}),[s,a,f.disabled,x,F,A,d]);return e.useEffect(()=>{const e=i._options.shouldUnregister||n;i.register(s,{...y.current.rules,...p(y.current.disabled)?{disabled:y.current.disabled}:{}});const t=(e,t)=>{const r=b(i._fields,e);r&&r._f&&(r._f.mount=t)};if(t(s,!0),e){const e=c(b(i._options.defaultValues,s));_(i._defaultValues,s,e),m(b(i._formValues,s))&&_(i._formValues,s,e)}return!o&&i.register(s),()=>{(o?e&&!i._state.action:e)?i.unregister(s):t(s,!1)}},[s,i,o,n]),e.useEffect(()=>{i._setDisabledField({disabled:a,name:s})},[a,s,i]),e.useMemo(()=>({field:S,formState:f,fieldState:v}),[S,f,v])}const q=e=>{const t={};for(const r of Object.keys(e))if(n(e[r])&&null!==e[r]){const s=q(e[r]);for(const e of Object.keys(s))t[`${r}.${e}`]=s[e]}else t[r]=e[r];return t},$="post";var H=(e,t,r,s,a)=>t?{...r[e],types:{...r[e]&&r[e].types?r[e].types:{},[s]:a||!0}}:{},z=e=>Array.isArray(e)?e:[e],J=()=>{let e=[];return{get observers(){return e},next:t=>{for(const r of e)r.next&&r.next(t)},subscribe:t=>(e.push(t),{unsubscribe:()=>{e=e.filter(e=>e!==t)}}),unsubscribe:()=>{e=[]}}},G=e=>i(e)||!n(e);function K(e,t,r=new WeakSet){if(G(e)||G(t))return e===t;if(a(e)&&a(t))return e.getTime()===t.getTime();const s=Object.keys(e),i=Object.keys(t);if(s.length!==i.length)return!1;if(r.has(e)||r.has(t))return!0;r.add(e),r.add(t);for(const n of s){const s=e[n];if(!i.includes(n))return!1;if("ref"!==n){const e=t[n];if(a(s)&&a(e)||o(s)&&o(e)||Array.isArray(s)&&Array.isArray(e)?!K(s,e,r):s!==e)return!1}}return!0}var Q=e=>o(e)&&!Object.keys(e).length,X=e=>"file"===e.type,Y=e=>"function"==typeof e,Z=e=>{if(!d)return!1;const t=e?e.ownerDocument:0;return e instanceof(t&&t.defaultView?t.defaultView.HTMLElement:HTMLElement)},ee=e=>"select-multiple"===e.type,te=e=>"radio"===e.type,re=e=>Z(e)&&e.isConnected;function se(e,t){const r=Array.isArray(t)?t:f(t)?[t]:g(t),s=1===r.length?e:function(e,t){const r=t.slice(0,-1).length;let s=0;for(;s<r;)e=m(e)?s++:e[t[s++]];return e}(e,r),a=r.length-1,i=r[a];return s&&delete s[i],0!==a&&(o(s)&&Q(s)||Array.isArray(s)&&function(e){for(const t in e)if(e.hasOwnProperty(t)&&!m(e[t]))return!1;return!0}(s))&&se(e,r.slice(0,-1)),e}var ae=e=>{for(const t in e)if(Y(e[t]))return!0;return!1};function ie(e,t={}){const r=Array.isArray(e);if(o(e)||r)for(const r in e)Array.isArray(e[r])||o(e[r])&&!ae(e[r])?(t[r]=Array.isArray(e[r])?[]:{},ie(e[r],t[r])):i(e[r])||(t[r]=!0);return t}function ne(e,t,r){const s=Array.isArray(e);if(o(e)||s)for(const s in e)Array.isArray(e[s])||o(e[s])&&!ae(e[s])?m(t)||G(r[s])?r[s]=Array.isArray(e[s])?ie(e[s],[]):{...ie(e[s])}:ne(e[s],i(t)?{}:t[s],r[s]):r[s]=!K(e[s],t[s]);return r}var oe=(e,t)=>ne(e,t,ie(t));const le={value:!1,isValid:!1},ue={value:!0,isValid:!0};var de=e=>{if(Array.isArray(e)){if(e.length>1){const t=e.filter(e=>e&&e.checked&&!e.disabled).map(e=>e.value);return{value:t,isValid:!!t.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!m(e[0].attributes.value)?m(e[0].value)||""===e[0].value?ue:{value:e[0].value,isValid:!0}:ue:le}return le},ce=(e,{valueAsNumber:t,valueAsDate:r,setValueAs:s})=>m(e)?e:t?""===e?NaN:e?+e:e:r&&L(e)?new Date(e):s?s(e):e;const fe={isValid:!1,value:null};var me=e=>Array.isArray(e)?e.reduce((e,t)=>t&&t.checked&&!t.disabled?{isValid:!0,value:t.value}:e,fe):fe;function ye(e){const t=e.ref;return X(t)?t.files:te(t)?me(e.refs).value:ee(t)?[...t.selectedOptions].map(({value:e})=>e):s(t)?de(e.refs).value:ce(m(t.value)?e.ref.value:t.value,e)}var ge=e=>e instanceof RegExp,be=e=>m(e)?e:ge(e)?e.source:o(e)?ge(e.value)?e.value.source:e.value:e,pe=e=>({isOnSubmit:!e||e===A,isOnBlur:e===x,isOnChange:e===F,isOnAll:e===w,isOnTouch:e===S});const _e="AsyncFunction";var he=e=>!!e&&!!e.validate&&!!(Y(e.validate)&&e.validate.constructor.name===_e||o(e.validate)&&Object.values(e.validate).find(e=>e.constructor.name===_e)),ve=(e,t,r)=>!r&&(t.watchAll||t.watch.has(e)||[...t.watch].some(t=>e.startsWith(t)&&/^\.\w+/.test(e.slice(t.length))));const Ve=(e,t,r,s)=>{for(const a of r||Object.keys(e)){const r=b(e,a);if(r){const{_f:e,...i}=r;if(e){if(e.refs&&e.refs[0]&&t(e.refs[0],a)&&!s)return!0;if(e.ref&&t(e.ref,e.name)&&!s)return!0;if(Ve(i,t))break}else if(o(i)&&Ve(i,t))break}}};function xe(e,t,r){const s=b(e,r);if(s||f(r))return{error:s,name:r};const a=r.split(".");for(;a.length;){const s=a.join("."),i=b(t,s),n=b(e,s);if(i&&!Array.isArray(i)&&r!==s)return{name:r};if(n&&n.type)return{name:s,error:n};if(n&&n.root&&n.root.type)return{name:`${s}.root`,error:n.root};a.pop()}return{name:r}}var Fe=(e,t,r)=>{const s=z(b(e,r));return _(s,"root",t[r]),_(e,r,s),e},Ae=e=>L(e);function Se(e,t,r="validate"){if(Ae(e)||Array.isArray(e)&&e.every(Ae)||p(e)&&!e)return{type:r,message:Ae(e)?e:"",ref:t}}var we=e=>o(e)&&!ge(e)?e:{value:e,message:""},ke=async(e,t,r,a,n,l)=>{const{ref:u,refs:d,required:c,maxLength:f,minLength:y,min:g,max:_,pattern:h,validate:v,name:V,valueAsNumber:x,mount:F}=e._f,A=b(r,V);if(!F||t.has(V))return{};const S=d?d[0]:u,w=e=>{n&&S.reportValidity&&(S.setCustomValidity(p(e)?"":e||""),S.reportValidity())},U={},T=te(u),N=s(u),R=T||N,B=(x||X(u))&&m(u.value)&&m(A)||Z(u)&&""===u.value||""===A||Array.isArray(A)&&!A.length,P=H.bind(null,V,a,U),I=(e,t,r,s=C,a=E)=>{const i=e?t:r;U[V]={type:e?s:a,message:i,ref:u,...P(e?s:a,i)}};if(l?!Array.isArray(A)||!A.length:c&&(!R&&(B||i(A))||p(A)&&!A||N&&!de(d).isValid||T&&!me(d).isValid)){const{value:e,message:t}=Ae(c)?{value:!!c,message:c}:we(c);if(e&&(U[V]={type:j,message:t,ref:S,...P(j,t)},!a))return w(t),U}if(!(B||i(g)&&i(_))){let e,t;const r=we(_),s=we(g);if(i(A)||isNaN(A)){const a=u.valueAsDate||new Date(A),i=e=>new Date((new Date).toDateString()+" "+e),n="time"==u.type,o="week"==u.type;L(r.value)&&A&&(e=n?i(A)>i(r.value):o?A>r.value:a>new Date(r.value)),L(s.value)&&A&&(t=n?i(A)<i(s.value):o?A<s.value:a<new Date(s.value))}else{const a=u.valueAsNumber||(A?+A:A);i(r.value)||(e=a>r.value),i(s.value)||(t=a<s.value)}if((e||t)&&(I(!!e,r.message,s.message,k,D),!a))return w(U[V].message),U}if((f||y)&&!B&&(L(A)||l&&Array.isArray(A))){const e=we(f),t=we(y),r=!i(e.value)&&A.length>+e.value,s=!i(t.value)&&A.length<+t.value;if((r||s)&&(I(r,e.message,t.message),!a))return w(U[V].message),U}if(h&&!B&&L(A)){const{value:e,message:t}=we(h);if(ge(e)&&!A.match(e)&&(U[V]={type:O,message:t,ref:u,...P(O,t)},!a))return w(t),U}if(v)if(Y(v)){const e=Se(await v(A,r),S);if(e&&(U[V]={...e,...P(M,e.message)},!a))return w(e.message),U}else if(o(v)){let e={};for(const t in v){if(!Q(e)&&!a)break;const s=Se(await v[t](A,r),S,t);s&&(e={...s,...P(t,s.message)},w(s.message),a&&(U[V]=e))}if(!Q(e)&&(U[V]={ref:S,...e},!a))return U}return w(!0),U};const De={mode:A,reValidateMode:F,shouldFocusError:!0};function Ce(e={}){let t,r={...De,...e},n={submitCount:0,isDirty:!1,isReady:!1,isLoading:Y(r.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:r.errors||{},disabled:r.disabled||!1},f={},g=(o(r.defaultValues)||o(r.values))&&c(r.defaultValues||r.values)||{},V=r.shouldUnregister?{}:c(g),x={action:!1,mount:!1,watch:!1},F={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},A=0;const S={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1};let k={...S};const D={array:J(),state:J()},C=r.criteriaMode===w,E=async e=>{if(!r.disabled&&(S.isValid||k.isValid||e)){const e=r.resolver?Q((await T()).errors):await N(f,!0);e!==n.isValid&&D.state.next({isValid:e})}},O=(e,t)=>{!r.disabled&&(S.isValidating||S.validatingFields||k.isValidating||k.validatingFields)&&((e||Array.from(F.mount)).forEach(e=>{e&&(t?_(n.validatingFields,e,t):se(n.validatingFields,e))}),D.state.next({validatingFields:n.validatingFields,isValidating:!Q(n.validatingFields)}))},j=(e,t,r,s)=>{const a=b(f,e);if(a){const i=b(V,e,m(r)?b(g,e):r);m(i)||s&&s.defaultChecked||t?_(V,e,t?i:ye(a._f)):I(e,i),x.mount&&E()}},M=(e,t,s,a,i)=>{let o=!1,l=!1;const u={name:e};if(!r.disabled){if(!s||a){(S.isDirty||k.isDirty)&&(l=n.isDirty,n.isDirty=u.isDirty=R(),o=l!==u.isDirty);const r=K(b(g,e),t);l=!!b(n.dirtyFields,e),r?se(n.dirtyFields,e):_(n.dirtyFields,e,!0),u.dirtyFields=n.dirtyFields,o=o||(S.dirtyFields||k.dirtyFields)&&l!==!r}if(s){const t=b(n.touchedFields,e);t||(_(n.touchedFields,e,s),u.touchedFields=n.touchedFields,o=o||(S.touchedFields||k.touchedFields)&&t!==s)}o&&i&&D.state.next(u)}return o?u:{}},U=(e,s,a,i)=>{const o=b(n.errors,e),l=(S.isValid||k.isValid)&&p(s)&&n.isValid!==s;var u;if(r.delayError&&a?(u=()=>((e,t)=>{_(n.errors,e,t),D.state.next({errors:n.errors})})(e,a),t=e=>{clearTimeout(A),A=setTimeout(u,e)},t(r.delayError)):(clearTimeout(A),t=null,a?_(n.errors,e,a):se(n.errors,e)),(a?!K(o,a):o)||!Q(i)||l){const t={...i,...l&&p(s)?{isValid:s}:{},errors:n.errors,name:e};n={...n,...t},D.state.next(t)}},T=async e=>{O(e,!0);const t=await r.resolver(V,r.context,((e,t,r,s)=>{const a={};for(const r of e){const e=b(t,r);e&&_(a,r,e._f)}return{criteriaMode:r,names:[...e],fields:a,shouldUseNativeValidation:s}})(e||F.mount,f,r.criteriaMode,r.shouldUseNativeValidation));return O(e),t},N=async(e,t,s={valid:!0})=>{for(const a in e){const i=e[a];if(i){const{_f:e,...o}=i;if(e){const o=F.array.has(e.name),l=i._f&&he(i._f);l&&S.validatingFields&&O([a],!0);const u=await ke(i,F.disabled,V,C,r.shouldUseNativeValidation&&!t,o);if(l&&S.validatingFields&&O([a]),u[e.name]&&(s.valid=!1,t))break;!t&&(b(u,e.name)?o?Fe(n.errors,u,e.name):_(n.errors,e.name,u[e.name]):se(n.errors,e.name))}!Q(o)&&await N(o,t,s)}}return s.valid},R=(e,t)=>!r.disabled&&(e&&t&&_(V,e,t),!K(ae(),g)),B=(e,t,r)=>P(e,F,{...x.mount?V:m(t)?g:L(e)?{[e]:t}:t},r,t),I=(e,t,r={})=>{const a=b(f,e);let n=t;if(a){const r=a._f;r&&(!r.disabled&&_(V,e,ce(t,r)),n=Z(r.ref)&&i(t)?"":t,ee(r.ref)?[...r.ref.options].forEach(e=>e.selected=n.includes(e.value)):r.refs?s(r.ref)?r.refs.forEach(e=>{e.defaultChecked&&e.disabled||(Array.isArray(n)?e.checked=!!n.find(t=>t===e.value):e.checked=n===e.value||!!n)}):r.refs.forEach(e=>e.checked=e.value===n):X(r.ref)?r.ref.value="":(r.ref.value=n,r.ref.type||D.state.next({name:e,values:c(V)})))}(r.shouldDirty||r.shouldTouch)&&M(e,n,r.shouldTouch,r.shouldDirty,!0),r.shouldValidate&&G(e)},W=(e,t,r)=>{for(const s in t){if(!t.hasOwnProperty(s))return;const i=t[s],n=e+"."+s,l=b(f,n);(F.array.has(e)||o(i)||l&&!l._f)&&!a(i)?W(n,i,r):I(n,i,r)}},q=(e,t,r={})=>{const s=b(f,e),a=F.array.has(e),o=c(t);_(V,e,o),a?(D.array.next({name:e,values:c(V)}),(S.isDirty||S.dirtyFields||k.isDirty||k.dirtyFields)&&r.shouldDirty&&D.state.next({name:e,dirtyFields:oe(g,V),isDirty:R(e,o)})):!s||s._f||i(o)?I(e,o,r):W(e,o,r),ve(e,F)&&D.state.next({...n}),D.state.next({name:x.mount?e:void 0,values:c(V)})},$=async e=>{x.mount=!0;const s=e.target;let i=s.name,o=!0;const u=b(f,i),d=e=>{o=Number.isNaN(e)||a(e)&&isNaN(e.getTime())||K(e,b(V,i,e))},m=pe(r.mode),y=pe(r.reValidateMode);if(u){let a,p;const x=s.type?ye(u._f):l(e),A=e.type===h||e.type===v,w=!((g=u._f).mount&&(g.required||g.min||g.max||g.maxLength||g.minLength||g.pattern||g.validate)||r.resolver||b(n.errors,i)||u._f.deps)||((e,t,r,s,a)=>!a.isOnAll&&(!r&&a.isOnTouch?!(t||e):(r?s.isOnBlur:a.isOnBlur)?!e:!(r?s.isOnChange:a.isOnChange)||e))(A,b(n.touchedFields,i),n.isSubmitted,y,m),j=ve(i,F,A);_(V,i,x),A?(u._f.onBlur&&u._f.onBlur(e),t&&t(0)):u._f.onChange&&u._f.onChange(e);const R=M(i,x,A),B=!Q(R)||j;if(!A&&D.state.next({name:i,type:e.type,values:c(V)}),w)return(S.isValid||k.isValid)&&("onBlur"===r.mode?A&&E():A||E()),B&&D.state.next({name:i,...j?{}:R});if(!A&&j&&D.state.next({...n}),r.resolver){const{errors:e}=await T([i]);if(d(x),o){const t=xe(n.errors,f,i),r=xe(e,f,t.name||i);a=r.error,i=r.name,p=Q(e)}}else O([i],!0),a=(await ke(u,F.disabled,V,C,r.shouldUseNativeValidation))[i],O([i]),d(x),o&&(a?p=!1:(S.isValid||k.isValid)&&(p=await N(f,!0)));o&&(u._f.deps&&G(u._f.deps),U(i,p,a,R))}var g},H=(e,t)=>{if(b(n.errors,t)&&e.focus)return e.focus(),1},G=async(e,t={})=>{let s,a;const i=z(e);if(r.resolver){const t=await(async e=>{const{errors:t}=await T(e);if(e)for(const r of e){const e=b(t,r);e?_(n.errors,r,e):se(n.errors,r)}else n.errors=t;return t})(m(e)?e:i);s=Q(t),a=e?!i.some(e=>b(t,e)):s}else e?(a=(await Promise.all(i.map(async e=>{const t=b(f,e);return await N(t&&t._f?{[e]:t}:t)}))).every(Boolean),(a||n.isValid)&&E()):a=s=await N(f);return D.state.next({...!L(e)||(S.isValid||k.isValid)&&s!==n.isValid?{}:{name:e},...r.resolver||!e?{isValid:s}:{},errors:n.errors}),t.shouldFocus&&!a&&Ve(f,H,e?i:F.mount),a},ae=e=>{const t={...x.mount?V:g};return m(e)?t:L(e)?b(t,e):e.map(e=>b(t,e))},ie=(e,t)=>({invalid:!!b((t||n).errors,e),isDirty:!!b((t||n).dirtyFields,e),error:b((t||n).errors,e),isValidating:!!b(n.validatingFields,e),isTouched:!!b((t||n).touchedFields,e)}),ne=(e,t,r)=>{const s=(b(f,e,{_f:{}})._f||{}).ref,a=b(n.errors,e)||{},{ref:i,message:o,type:l,...u}=a;_(n.errors,e,{...u,...t,ref:s}),D.state.next({name:e,errors:n.errors,isValid:!1}),r&&r.shouldFocus&&s&&s.focus&&s.focus()},le=e=>D.state.subscribe({next:t=>{var r,s,a;r=e.name,s=t.name,a=e.exact,r&&s&&r!==s&&!z(r).some(e=>e&&(a?e===s:e.startsWith(s)||s.startsWith(e)))||!((e,t,r,s)=>{r(e);const{name:a,...i}=e;return Q(i)||Object.keys(i).length>=Object.keys(t).length||Object.keys(i).find(e=>t[e]===(!s||w))})(t,e.formState||S,Se,e.reRenderRoot)||e.callback({values:{...V},...n,...t})}}).unsubscribe,ue=(e,t={})=>{for(const s of e?z(e):F.mount)F.mount.delete(s),F.array.delete(s),t.keepValue||(se(f,s),se(V,s)),!t.keepError&&se(n.errors,s),!t.keepDirty&&se(n.dirtyFields,s),!t.keepTouched&&se(n.touchedFields,s),!t.keepIsValidating&&se(n.validatingFields,s),!r.shouldUnregister&&!t.keepDefaultValue&&se(g,s);D.state.next({values:c(V)}),D.state.next({...n,...t.keepDirty?{isDirty:R()}:{}}),!t.keepIsValid&&E()},de=({disabled:e,name:t})=>{(p(e)&&x.mount||e||F.disabled.has(t))&&(e?F.disabled.add(t):F.disabled.delete(t))},fe=(e,t={})=>{let a=b(f,e);const i=p(t.disabled)||p(r.disabled);return _(f,e,{...a||{},_f:{...a&&a._f?a._f:{ref:{name:e}},name:e,mount:!0,...t}}),F.mount.add(e),a?de({disabled:p(t.disabled)?t.disabled:r.disabled,name:e}):j(e,!0,t.value),{...i?{disabled:t.disabled||r.disabled}:{},...r.progressive?{required:!!t.required,min:be(t.min),max:be(t.max),minLength:be(t.minLength),maxLength:be(t.maxLength),pattern:be(t.pattern)}:{},name:e,onChange:$,onBlur:$,ref:i=>{if(i){fe(e,t),a=b(f,e);const r=m(i.value)&&i.querySelectorAll&&i.querySelectorAll("input,select,textarea")[0]||i,n=(e=>te(e)||s(e))(r),o=a._f.refs||[];if(n?o.find(e=>e===r):r===a._f.ref)return;_(f,e,{_f:{...a._f,...n?{refs:[...o.filter(re),r,...Array.isArray(b(g,e))?[{}]:[]],ref:{type:r.type,name:e}}:{ref:r}}}),j(e,!1,void 0,r)}else a=b(f,e,{}),a._f&&(a._f.mount=!1),(r.shouldUnregister||t.shouldUnregister)&&(!u(F.array,e)||!x.action)&&F.unMount.add(e)}}},me=()=>r.shouldFocusError&&Ve(f,H,F.mount),ge=(e,t)=>async s=>{let a;s&&(s.preventDefault&&s.preventDefault(),s.persist&&s.persist());let i=c(V);if(D.state.next({isSubmitting:!0}),r.resolver){const{errors:e,values:t}=await T();n.errors=e,i=c(t)}else await N(f);if(F.disabled.size)for(const e of F.disabled)se(i,e);if(se(n.errors,"root"),Q(n.errors)){D.state.next({errors:{}});try{await e(i,s)}catch(e){a=e}}else t&&await t({...n.errors},s),me(),setTimeout(me);if(D.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:Q(n.errors)&&!a,submitCount:n.submitCount+1,errors:n.errors}),a)throw a},_e=(e,t={})=>{const s=e?c(e):g,a=c(s),i=Q(e),o=i?g:a;if(t.keepDefaultValues||(g=s),!t.keepValues){if(t.keepDirtyValues){const e=new Set([...F.mount,...Object.keys(oe(g,V))]);for(const t of Array.from(e))b(n.dirtyFields,t)?_(o,t,b(V,t)):q(t,b(o,t))}else{if(d&&m(e))for(const e of F.mount){const t=b(f,e);if(t&&t._f){const e=Array.isArray(t._f.refs)?t._f.refs[0]:t._f.ref;if(Z(e)){const t=e.closest("form");if(t){t.reset();break}}}}if(t.keepFieldsRef)for(const e of F.mount)q(e,b(o,e));else f={}}V=r.shouldUnregister?t.keepDefaultValues?c(g):{}:c(o),D.array.next({values:{...o}}),D.state.next({values:{...o}})}F={mount:t.keepDirtyValues?F.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},x.mount=!S.isValid||!!t.keepIsValid||!!t.keepDirtyValues,x.watch=!!r.shouldUnregister,D.state.next({submitCount:t.keepSubmitCount?n.submitCount:0,isDirty:!i&&(t.keepDirty?n.isDirty:!(!t.keepDefaultValues||K(e,g))),isSubmitted:!!t.keepIsSubmitted&&n.isSubmitted,dirtyFields:i?{}:t.keepDirtyValues?t.keepDefaultValues&&V?oe(g,V):n.dirtyFields:t.keepDefaultValues&&e?oe(g,e):t.keepDirty?n.dirtyFields:{},touchedFields:t.keepTouched?n.touchedFields:{},errors:t.keepErrors?n.errors:{},isSubmitSuccessful:!!t.keepIsSubmitSuccessful&&n.isSubmitSuccessful,isSubmitting:!1})},Ae=(e,t)=>_e(Y(e)?e(V):e,t),Se=e=>{n={...n,...e}},we={control:{register:fe,unregister:ue,getFieldState:ie,handleSubmit:ge,setError:ne,_subscribe:le,_runSchema:T,_focusError:me,_getWatch:B,_getDirty:R,_setValid:E,_setFieldArray:(e,t=[],s,a,i=!0,o=!0)=>{if(a&&s&&!r.disabled){if(x.action=!0,o&&Array.isArray(b(f,e))){const t=s(b(f,e),a.argA,a.argB);i&&_(f,e,t)}if(o&&Array.isArray(b(n.errors,e))){const t=s(b(n.errors,e),a.argA,a.argB);i&&_(n.errors,e,t),((e,t)=>{!y(b(e,t)).length&&se(e,t)})(n.errors,e)}if((S.touchedFields||k.touchedFields)&&o&&Array.isArray(b(n.touchedFields,e))){const t=s(b(n.touchedFields,e),a.argA,a.argB);i&&_(n.touchedFields,e,t)}(S.dirtyFields||k.dirtyFields)&&(n.dirtyFields=oe(g,V)),D.state.next({name:e,isDirty:R(e,t),dirtyFields:n.dirtyFields,errors:n.errors,isValid:n.isValid})}else _(V,e,t)},_setDisabledField:de,_setErrors:e=>{n.errors=e,D.state.next({errors:n.errors,isValid:!1})},_getFieldArray:e=>y(b(x.mount?V:g,e,r.shouldUnregister?b(g,e,[]):[])),_reset:_e,_resetDefaultValues:()=>Y(r.defaultValues)&&r.defaultValues().then(e=>{Ae(e,r.resetOptions),D.state.next({isLoading:!1})}),_removeUnmounted:()=>{for(const e of F.unMount){const t=b(f,e);t&&(t._f.refs?t._f.refs.every(e=>!re(e)):!re(t._f.ref))&&ue(e)}F.unMount=new Set},_disableForm:e=>{p(e)&&(D.state.next({disabled:e}),Ve(f,(t,r)=>{const s=b(f,r);s&&(t.disabled=s._f.disabled||e,Array.isArray(s._f.refs)&&s._f.refs.forEach(t=>{t.disabled=s._f.disabled||e}))},0,!1))},_subjects:D,_proxyFormState:S,get _fields(){return f},get _formValues(){return V},get _state(){return x},set _state(e){x=e},get _defaultValues(){return g},get _names(){return F},set _names(e){F=e},get _formState(){return n},get _options(){return r},set _options(e){r={...r,...e}}},subscribe:e=>(x.mount=!0,k={...k,...e.formState},le({...e,formState:k})),trigger:G,register:fe,handleSubmit:ge,watch:(e,t)=>Y(e)?D.state.subscribe({next:r=>e(B(void 0,t),r)}):B(e,t,!0),setValue:q,getValues:ae,reset:Ae,resetField:(e,t={})=>{b(f,e)&&(m(t.defaultValue)?q(e,c(b(g,e))):(q(e,t.defaultValue),_(g,e,c(t.defaultValue))),t.keepTouched||se(n.touchedFields,e),t.keepDirty||(se(n.dirtyFields,e),n.isDirty=t.defaultValue?R(e,c(b(g,e))):R()),t.keepError||(se(n.errors,e),S.isValid&&E()),D.state.next({...n}))},clearErrors:e=>{e&&z(e).forEach(e=>se(n.errors,e)),D.state.next({errors:e?n.errors:{}})},unregister:ue,setError:ne,setFocus:(e,t={})=>{const r=b(f,e),s=r&&r._f;if(s){const e=s.refs?s.refs[0]:s.ref;e.focus&&(e.focus(),t.shouldSelect&&Y(e.select)&&e.select())}},getFieldState:ie};return{...we,formControl:we}}var Ee=()=>{if("undefined"!=typeof crypto&&crypto.randomUUID)return crypto.randomUUID();const e="undefined"==typeof performance?Date.now():1e3*performance.now();return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,t=>{const r=(16*Math.random()+e)%16|0;return("x"==t?r:3&r|8).toString(16)})},Oe=(e,t,r={})=>r.shouldFocus||m(r.shouldFocus)?r.focusName||`${e}.${m(r.focusIndex)?t:r.focusIndex}.`:"",je=(e,t)=>[...e,...z(t)],Me=e=>Array.isArray(e)?e.map(()=>{}):void 0;function Ue(e,t,r){return[...e.slice(0,t),...z(r),...e.slice(t)]}var Te=(e,t,r)=>Array.isArray(e)?(m(e[r])&&(e[r]=void 0),e.splice(r,0,e.splice(t,1)[0]),e):[],Ne=(e,t)=>[...z(t),...z(e)];var Re=(e,t)=>m(t)?[]:function(e,t){let r=0;const s=[...e];for(const e of t)s.splice(e-r,1),r++;return y(s).length?s:[]}(e,z(t).sort((e,t)=>e-t)),Be=(e,t,r)=>{[e[t],e[r]]=[e[r],e[t]]},Le=(e,t,r)=>(e[t]=r,e);exports.Controller=e=>e.render(W(e)),exports.Form=function(t){const r=T(),[s,a]=e.useState(!1),{control:i=r.control,onSubmit:n,children:o,action:l,method:u=$,headers:d,encType:c,onError:f,render:m,onSuccess:y,validateStatus:g,...b}=t,p=async e=>{let r=!1,s="";await i.handleSubmit(async t=>{const a=new FormData;let o="";try{o=JSON.stringify(t)}catch(e){}const m=q(i._formValues);for(const e in m)a.append(e,m[e]);if(n&&await n({data:t,event:e,method:u,formData:a,formDataJson:o}),l)try{const e=[d&&d["Content-Type"],c].some(e=>e&&e.includes("json")),t=await fetch(String(l),{method:u,headers:{...d,...c?{"Content-Type":c}:{}},body:e?o:a});t&&(g?!g(t.status):t.status<200||t.status>=300)?(r=!0,f&&f({response:t}),s=String(t.status)):y&&y({response:t})}catch(e){r=!0,f&&f({error:e})}})(e),r&&t.control&&(t.control._subjects.state.next({isSubmitSuccessful:!1}),t.control.setError("root.server",{type:s}))};return e.useEffect(()=>{a(!0)},[]),m?e.createElement(e.Fragment,null,m({submit:p})):e.createElement("form",{noValidate:s,action:l,method:u,encType:c,onSubmit:p,...b},o)},exports.FormProvider=t=>{const{children:r,...s}=t;return e.createElement(U.Provider,{value:s},r)},exports.appendErrors=H,exports.createFormControl=Ce,exports.get=b,exports.set=_,exports.useController=W,exports.useFieldArray=function(t){const r=T(),{control:s=r.control,name:a,keyName:i="id",shouldUnregister:n,rules:o}=t,[l,u]=e.useState(s._getFieldArray(a)),d=e.useRef(s._getFieldArray(a).map(Ee)),f=e.useRef(l),m=e.useRef(a),y=e.useRef(!1);m.current=a,f.current=l,s._names.array.add(a),o&&s.register(a,o),R(()=>s._subjects.array.subscribe({next:({values:e,name:t})=>{if(t===m.current||!t){const t=b(e,m.current);Array.isArray(t)&&(u(t),d.current=t.map(Ee))}}}).unsubscribe,[s]);const g=e.useCallback(e=>{y.current=!0,s._setFieldArray(a,e)},[s,a]);return e.useEffect(()=>{if(s._state.action=!1,ve(a,s._names)&&s._subjects.state.next({...s._formState}),y.current&&(!pe(s._options.mode).isOnSubmit||s._formState.isSubmitted)&&!pe(s._options.reValidateMode).isOnSubmit)if(s._options.resolver)s._runSchema([a]).then(e=>{const t=b(e.errors,a),r=b(s._formState.errors,a);(r?!t&&r.type||t&&(r.type!==t.type||r.message!==t.message):t&&t.type)&&(t?_(s._formState.errors,a,t):se(s._formState.errors,a),s._subjects.state.next({errors:s._formState.errors}))});else{const e=b(s._fields,a);!e||!e._f||pe(s._options.reValidateMode).isOnSubmit&&pe(s._options.mode).isOnSubmit||ke(e,s._names.disabled,s._formValues,s._options.criteriaMode===w,s._options.shouldUseNativeValidation,!0).then(e=>!Q(e)&&s._subjects.state.next({errors:Fe(s._formState.errors,e,a)}))}s._subjects.state.next({name:a,values:c(s._formValues)}),s._names.focus&&Ve(s._fields,(e,t)=>{if(s._names.focus&&t.startsWith(s._names.focus)&&e.focus)return e.focus(),1}),s._names.focus="",s._setValid(),y.current=!1},[l,a,s]),e.useEffect(()=>(!b(s._formValues,a)&&s._setFieldArray(a),()=>{s._options.shouldUnregister||n?s.unregister(a):((e,t)=>{const r=b(s._fields,e);r&&r._f&&(r._f.mount=t)})(a,!1)}),[a,s,i,n]),{swap:e.useCallback((e,t)=>{const r=s._getFieldArray(a);Be(r,e,t),Be(d.current,e,t),g(r),u(r),s._setFieldArray(a,r,Be,{argA:e,argB:t},!1)},[g,a,s]),move:e.useCallback((e,t)=>{const r=s._getFieldArray(a);Te(r,e,t),Te(d.current,e,t),g(r),u(r),s._setFieldArray(a,r,Te,{argA:e,argB:t},!1)},[g,a,s]),prepend:e.useCallback((e,t)=>{const r=z(c(e)),i=Ne(s._getFieldArray(a),r);s._names.focus=Oe(a,0,t),d.current=Ne(d.current,r.map(Ee)),g(i),u(i),s._setFieldArray(a,i,Ne,{argA:Me(e)})},[g,a,s]),append:e.useCallback((e,t)=>{const r=z(c(e)),i=je(s._getFieldArray(a),r);s._names.focus=Oe(a,i.length-1,t),d.current=je(d.current,r.map(Ee)),g(i),u(i),s._setFieldArray(a,i,je,{argA:Me(e)})},[g,a,s]),remove:e.useCallback(e=>{const t=Re(s._getFieldArray(a),e);d.current=Re(d.current,e),g(t),u(t),!Array.isArray(b(s._fields,a))&&_(s._fields,a,void 0),s._setFieldArray(a,t,Re,{argA:e})},[g,a,s]),insert:e.useCallback((e,t,r)=>{const i=z(c(t)),n=Ue(s._getFieldArray(a),e,i);s._names.focus=Oe(a,e,r),d.current=Ue(d.current,e,i.map(Ee)),g(n),u(n),s._setFieldArray(a,n,Ue,{argA:e,argB:Me(t)})},[g,a,s]),update:e.useCallback((e,t)=>{const r=c(t),i=Le(s._getFieldArray(a),e,r);d.current=[...i].map((t,r)=>t&&r!==e?d.current[r]:Ee()),g(i),u([...i]),s._setFieldArray(a,i,Le,{argA:e,argB:r},!0,!1)},[g,a,s]),replace:e.useCallback(e=>{const t=z(c(e));d.current=t.map(Ee),g([...t]),u([...t]),s._setFieldArray(a,[...t],e=>e,{},!0,!1)},[g,a,s]),fields:e.useMemo(()=>l.map((e,t)=>({...e,[i]:d.current[t]||Ee()})),[l,i])}},exports.useForm=function(t={}){const r=e.useRef(void 0),s=e.useRef(void 0),[a,i]=e.useState({isDirty:!1,isValidating:!1,isLoading:Y(t.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:t.errors||{},disabled:t.disabled||!1,isReady:!1,defaultValues:Y(t.defaultValues)?void 0:t.defaultValues});if(!r.current)if(t.formControl)r.current={...t.formControl,formState:a},t.defaultValues&&!Y(t.defaultValues)&&t.formControl.reset(t.defaultValues,t.resetOptions);else{const{formControl:e,...s}=Ce(t);r.current={...s,formState:a}}const n=r.current.control;return n._options=t,R(()=>{const e=n._subscribe({formState:n._proxyFormState,callback:()=>i({...n._formState}),reRenderRoot:!0});return i(e=>({...e,isReady:!0})),n._formState.isReady=!0,e},[n]),e.useEffect(()=>n._disableForm(t.disabled),[n,t.disabled]),e.useEffect(()=>{t.mode&&(n._options.mode=t.mode),t.reValidateMode&&(n._options.reValidateMode=t.reValidateMode)},[n,t.mode,t.reValidateMode]),e.useEffect(()=>{t.errors&&(n._setErrors(t.errors),n._focusError())},[n,t.errors]),e.useEffect(()=>{t.shouldUnregister&&n._subjects.state.next({values:n._getWatch()})},[n,t.shouldUnregister]),e.useEffect(()=>{if(n._proxyFormState.isDirty){const e=n._getDirty();e!==a.isDirty&&n._subjects.state.next({isDirty:e})}},[n,a.isDirty]),e.useEffect(()=>{t.values&&!K(t.values,s.current)?(n._reset(t.values,{keepFieldsRef:!0,...n._options.resetOptions}),s.current=t.values,i(e=>({...e}))):n._resetDefaultValues()},[n,t.values]),e.useEffect(()=>{n._state.mount||(n._setValid(),n._state.mount=!0),n._state.watch&&(n._state.watch=!1,n._subjects.state.next({...n._formState})),n._removeUnmounted()}),r.current.formState=N(a,n),r.current},exports.useFormContext=T,exports.useFormState=B,exports.useWatch=I;
//# sourceMappingURL=index.cjs.js.map
