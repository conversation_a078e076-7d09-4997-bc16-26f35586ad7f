var e=require("@hookform/resolvers"),r=require("react-hook-form");exports.joiResolver=function(t,n,o){return void 0===n&&(n={abortEarly:!1}),void 0===o&&(o={}),function(a,i,s){try{var u=function(){return l.error?{values:{},errors:e.toNestErrors((t=l.error,n=!s.shouldUseNativeValidation&&"all"===s.criteriaMode,t.details.length?t.details.reduce(function(e,t){var o=t.path.join(".");if(e[o]||(e[o]={message:t.message,type:t.type}),n){var a=e[o].types,i=a&&a[t.type];e[o]=r.appendErrors(o,n,e,t.type,i?[].concat(i,t.message):t.message)}return e},{}):{}),s)}:(s.shouldUseNativeValidation&&e.validateFieldsNatively({},s),{errors:{},values:l.value});var t,n},c=Object.assign({},n,{context:i}),l={},v=function(){if("sync"===o.mode)l=t.validate(a,c);else{var e=function(e,r){try{var t=e()}catch(e){return r(e)}return t&&t.then?t.then(void 0,r):t}(function(){return Promise.resolve(t.validateAsync(a,c)).then(function(e){l.value=e})},function(e){l.error=e});if(e&&e.then)return e.then(function(){})}}();return Promise.resolve(v&&v.then?v.then(u):u())}catch(e){return Promise.reject(e)}}};
//# sourceMappingURL=joi.js.map
