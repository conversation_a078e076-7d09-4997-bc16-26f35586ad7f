import { useState } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import {
  Heart,
  ShoppingCart,
  Star,
  Eye
} from 'lucide-react';
import Card from '../ui/Card';
import Button from '../ui/Button';

const ProductCard = ({ 
  product, 
  onAddToCart, 
  onAddToWishlist, 
  isInWishlist = false,
  className = '' 
}) => {
  const [isImageLoaded, setIsImageLoaded] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  const {
    _id,
    name,
    price,
    originalPrice,
    discount,
    images,
    rating,
    brand,
    category,
    stock,
    isFeatured
  } = product;

  const primaryImage = images?.[currentImageIndex]?.url || images?.[0]?.url || '/placeholder-product.jpg';
  const hasDiscount = discount > 0 && originalPrice > price;
  const isOutOfStock = stock?.quantity <= 0;
  const discountPercentage = hasDiscount ? Math.round(((originalPrice - price) / originalPrice) * 100) : 0;

  const handleAddToCart = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (!isOutOfStock && onAddToCart) {
      onAddToCart(product);
    }
  };

  const handleAddToWishlist = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (onAddToWishlist) {
      onAddToWishlist(product);
    }
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.3 }
    },
    hover: {
      y: -5,
      transition: { duration: 0.2 }
    }
  };

  const imageVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 }
  };

  const overlayVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 }
  };

  return (
    <motion.div
      className={className}
      variants={cardVariants}
      initial="hidden"
      animate="visible"
      whileHover="hover"
    >
      <Link to={`/products/${_id}`}>
        <Card className="group overflow-hidden h-full flex flex-col bg-white/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-2xl transition-all duration-300">
          {/* Image Container */}
          <div className="relative aspect-square overflow-hidden bg-gray-100">
            {/* Badges */}
            <div className="absolute top-2 left-2 z-10 flex flex-col space-y-1">
              {isFeatured && (
                <span className="bg-blue-600 text-white text-xs px-2 py-1 rounded-full font-medium">
                  Featured
                </span>
              )}
              {hasDiscount && (
                <span className="bg-red-600 text-white text-xs px-2 py-1 rounded-full font-medium">
                  -{discountPercentage}%
                </span>
              )}
              {isOutOfStock && (
                <span className="bg-gray-600 text-white text-xs px-2 py-1 rounded-full font-medium">
                  Out of Stock
                </span>
              )}
            </div>

            {/* Wishlist Button */}
            <motion.button
              className="absolute top-2 right-2 z-10 p-2 bg-white rounded-full shadow-md opacity-0 group-hover:opacity-100 transition-opacity"
              onClick={handleAddToWishlist}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <Heart 
                className={`h-4 w-4 ${isInWishlist ? 'fill-red-500 text-red-500' : 'text-gray-600'}`} 
              />
            </motion.button>

            {/* Product Image */}
            <motion.img
              src={primaryImage}
              alt={name}
              className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
              variants={imageVariants}
              initial="hidden"
              animate={isImageLoaded ? "visible" : "hidden"}
              onLoad={() => setIsImageLoaded(true)}
            />

            {/* Image dots for multiple images */}
            {images && images.length > 1 && (
              <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 flex space-x-1">
                {images.slice(0, 3).map((_, index) => (
                  <button
                    key={index}
                    className={`w-2 h-2 rounded-full transition-colors ${
                      index === currentImageIndex ? 'bg-white' : 'bg-white/50'
                    }`}
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      setCurrentImageIndex(index);
                    }}
                  />
                ))}
              </div>
            )}

            {/* Hover Overlay */}
            <motion.div
              className="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
              variants={overlayVariants}
            >
              <div className="flex space-x-2">
                <Button
                  size="sm"
                  variant="secondary"
                  className="bg-white text-gray-900 hover:bg-gray-100"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    // Quick view functionality
                  }}
                >
                  <Eye className="h-4 w-4 mr-1" />
                  Quick View
                </Button>
              </div>
            </motion.div>
          </div>

          {/* Product Info */}
          <div className="p-4 flex-1 flex flex-col">
            {/* Brand & Category */}
            <div className="flex items-center justify-between text-xs text-gray-500 mb-1">
              <span>{brand}</span>
              <span>{category}</span>
            </div>

            {/* Product Name */}
            <h3 className="font-medium text-gray-900 mb-2 line-clamp-2 group-hover:text-blue-600 transition-colors">
              {name}
            </h3>

            {/* Rating */}
            {rating && rating.count > 0 && (
              <div className="flex items-center mb-2">
                <div className="flex items-center">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`h-3 w-3 ${
                        i < Math.floor(rating.average)
                          ? 'text-yellow-400 fill-current'
                          : 'text-gray-300'
                      }`}
                    />
                  ))}
                </div>
                <span className="text-xs text-gray-500 ml-1">
                  ({rating.count})
                </span>
              </div>
            )}

            {/* Price */}
            <div className="flex items-center space-x-2 mb-3">
              <span className="text-lg font-bold text-gray-900">
                ${price.toFixed(2)}
              </span>
              {hasDiscount && (
                <span className="text-sm text-gray-500 line-through">
                  ${originalPrice.toFixed(2)}
                </span>
              )}
            </div>

            {/* Add to Cart Button */}
            <div className="mt-auto">
              <Button
                className="w-full"
                onClick={handleAddToCart}
                disabled={isOutOfStock}
                size="sm"
              >
                <ShoppingCart className="h-4 w-4 mr-2" />
                {isOutOfStock ? 'Out of Stock' : 'Add to Cart'}
              </Button>
            </div>
          </div>
        </Card>
      </Link>
    </motion.div>
  );
};

export default ProductCard;
