@echo off
echo Starting AI E-Commerce Development Environment...
echo.

echo Installing dependencies...
echo.

echo [1/3] Installing root dependencies...
call npm install

echo [2/3] Installing server dependencies...
cd server
call npm install
cd ..

echo [3/3] Installing client dependencies...
cd client\ecommerce
call npm install
cd ..\..

echo.
echo Dependencies installed successfully!
echo.

echo Starting all services...
echo.

echo Killing any existing processes on ports 5000, 5173, 8000...
npx kill-port 5000 5173 8000 2>nul
timeout /t 2 /nobreak > nul

echo Starting development servers in separate windows...

start "AI Service" cmd /k "cd ai-service && python app.py"
timeout /t 3 /nobreak > nul

start "Backend Server" cmd /k "cd server && npm run dev"
timeout /t 3 /nobreak > nul

start "Frontend Client" cmd /k "cd client\ecommerce && npm run dev"

echo.
echo All services are starting...
echo.
echo Frontend: http://localhost:5173
echo Backend: http://localhost:5000
echo AI Service: http://localhost:8000
echo.
echo Press any key to exit...
pause > nul
