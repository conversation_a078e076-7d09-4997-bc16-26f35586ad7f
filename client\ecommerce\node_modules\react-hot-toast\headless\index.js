"use strict";var P=Object.defineProperty;var _=Object.getOwnPropertyDescriptor;var v=Object.getOwnPropertyNames;var I=Object.prototype.hasOwnProperty;var M=(e,t)=>{for(var r in t)P(e,r,{get:t[r],enumerable:!0})},U=(e,t,r,a)=>{if(t&&typeof t=="object"||typeof t=="function")for(let n of v(t))!I.call(e,n)&&n!==r&&P(e,n,{get:()=>t[n],enumerable:!(a=_(t,n))||a.enumerable});return e};var F=e=>U(P({},"__esModule",{value:!0}),e);var j={};M(j,{default:()=>Y,resolveValue:()=>m,toast:()=>s,useToaster:()=>V,useToasterStore:()=>g});module.exports=F(j);var w=e=>typeof e=="function",m=(e,t)=>w(e)?e(t):e;var R=(()=>{let e=0;return()=>(++e).toString()})(),J=(()=>{let e;return()=>{if(e===void 0&&typeof window<"u"){let t=matchMedia("(prefers-reduced-motion: reduce)");e=!t||t.matches}return e}})();var p=require("react"),N=20;var h=(e,t)=>{switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,N)};case 1:return{...e,toasts:e.toasts.map(o=>o.id===t.toast.id?{...o,...t.toast}:o)};case 2:let{toast:r}=t;return h(e,{type:e.toasts.find(o=>o.id===r.id)?1:0,toast:r});case 3:let{toastId:a}=t;return{...e,toasts:e.toasts.map(o=>o.id===a||a===void 0?{...o,dismissed:!0,visible:!1}:o)};case 4:return t.toastId===void 0?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(o=>o.id!==t.toastId)};case 5:return{...e,pausedAt:t.time};case 6:let n=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map(o=>({...o,pauseDuration:o.pauseDuration+n}))}}},A=[],l={toasts:[],pausedAt:void 0},c=e=>{l=h(l,e),A.forEach(t=>{t(l)})},k={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},g=(e={})=>{let[t,r]=(0,p.useState)(l),a=(0,p.useRef)(l);(0,p.useEffect)(()=>(a.current!==l&&r(l),A.push(r),()=>{let o=A.indexOf(r);o>-1&&A.splice(o,1)}),[]);let n=t.toasts.map(o=>{var T,i,d;return{...e,...e[o.type],...o,removeDelay:o.removeDelay||((T=e[o.type])==null?void 0:T.removeDelay)||(e==null?void 0:e.removeDelay),duration:o.duration||((i=e[o.type])==null?void 0:i.duration)||(e==null?void 0:e.duration)||k[o.type],style:{...e.style,...(d=e[o.type])==null?void 0:d.style,...o.style}}});return{...t,toasts:n}};var H=(e,t="blank",r)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...r,id:(r==null?void 0:r.id)||R()}),y=e=>(t,r)=>{let a=H(t,e,r);return c({type:2,toast:a}),a.id},s=(e,t)=>y("blank")(e,t);s.error=y("error");s.success=y("success");s.loading=y("loading");s.custom=y("custom");s.dismiss=e=>{c({type:3,toastId:e})};s.remove=e=>c({type:4,toastId:e});s.promise=(e,t,r)=>{let a=s.loading(t.loading,{...r,...r==null?void 0:r.loading});return typeof e=="function"&&(e=e()),e.then(n=>{let o=t.success?m(t.success,n):void 0;return o?s.success(o,{id:a,...r,...r==null?void 0:r.success}):s.dismiss(a),n}).catch(n=>{let o=t.error?m(t.error,n):void 0;o?s.error(o,{id:a,...r,...r==null?void 0:r.error}):s.dismiss(a)}),e};var f=require("react");var L=(e,t)=>{c({type:1,toast:{id:e,height:t}})},Q=()=>{c({type:5,time:Date.now()})},S=new Map,B=1e3,W=(e,t=B)=>{if(S.has(e))return;let r=setTimeout(()=>{S.delete(e),c({type:4,toastId:e})},t);S.set(e,r)},V=e=>{let{toasts:t,pausedAt:r}=g(e);(0,f.useEffect)(()=>{if(r)return;let o=Date.now(),T=t.map(i=>{if(i.duration===1/0)return;let d=(i.duration||0)+i.pauseDuration-(o-i.createdAt);if(d<0){i.visible&&s.dismiss(i.id);return}return setTimeout(()=>s.dismiss(i.id),d)});return()=>{T.forEach(i=>i&&clearTimeout(i))}},[t,r]);let a=(0,f.useCallback)(()=>{r&&c({type:6,time:Date.now()})},[r]),n=(0,f.useCallback)((o,T)=>{let{reverseOrder:i=!1,gutter:d=8,defaultPosition:b}=T||{},D=t.filter(u=>(u.position||b)===(o.position||b)&&u.height),x=D.findIndex(u=>u.id===o.id),E=D.filter((u,O)=>O<x&&u.visible).length;return D.filter(u=>u.visible).slice(...i?[E+1]:[0,E]).reduce((u,O)=>u+(O.height||0)+d,0)},[t]);return(0,f.useEffect)(()=>{t.forEach(o=>{if(o.dismissed)W(o.id,o.removeDelay);else{let T=S.get(o.id);T&&(clearTimeout(T),S.delete(o.id))}})},[t]),{toasts:t,handlers:{updateHeight:L,startPause:Q,endPause:a,calculateOffset:n}}};var Y=s;0&&(module.exports={resolveValue,toast,useToaster,useToasterStore});
//# sourceMappingURL=index.js.map