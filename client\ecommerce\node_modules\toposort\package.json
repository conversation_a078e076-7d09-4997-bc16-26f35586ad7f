{"name": "toposort", "version": "2.0.2", "description": "Topological sort of directed ascyclic graphs (like dependecy lists)", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "https://github.com/marcelklehr/toposort.git"}, "devDependencies": {"vows": "0.7.x"}, "keywords": ["topological", "sort", "sorting", "graphs", "graph", "dependency", "list", "dependencies", "acyclic"], "author": "<PERSON> <<EMAIL>>", "license": "MIT"}