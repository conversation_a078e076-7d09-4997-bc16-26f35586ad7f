import { createContext, useContext, useReducer, useEffect } from 'react';
import { authAPI, cartAPI } from '../services/api';
import toast from 'react-hot-toast';

// Initial state
const initialState = {
  user: null,
  isAuthenticated: false,
  loading: true,
  cart: {
    items: [],
    totalItems: 0,
    totalPrice: 0
  },
  wishlist: [],
  theme: 'light'
};

// Action types
const actionTypes = {
  SET_LOADING: 'SET_LOADING',
  SET_USER: 'SET_USER',
  LOGOUT: 'LOGOUT',
  SET_CART: 'SET_CART',
  ADD_TO_CART: 'ADD_TO_CART',
  UPDATE_CART_ITEM: 'UPDATE_CART_ITEM',
  REMOVE_FROM_CART: 'REMOVE_FROM_CART',
  CLEAR_CART: 'CLEAR_CART',
  SET_WISHLIST: 'SET_WISHLIST',
  ADD_TO_WISHLIST: 'ADD_TO_WISHLIST',
  REMOVE_FROM_WISHLIST: 'REMOVE_FROM_WISHLIST',
  SET_THEME: 'SET_THEME'
};

// Reducer function
const appReducer = (state, action) => {
  switch (action.type) {
    case actionTypes.SET_LOADING:
      return {
        ...state,
        loading: action.payload
      };

    case actionTypes.SET_USER:
      return {
        ...state,
        user: action.payload,
        isAuthenticated: !!action.payload,
        loading: false
      };

    case actionTypes.LOGOUT:
      return {
        ...state,
        user: null,
        isAuthenticated: false,
        cart: initialState.cart,
        wishlist: []
      };

    case actionTypes.SET_CART:
      return {
        ...state,
        cart: action.payload
      };

    case actionTypes.ADD_TO_CART:
      const existingItemIndex = state.cart.items.findIndex(
        item => item.product._id === action.payload.product._id
      );

      let updatedItems;
      if (existingItemIndex > -1) {
        updatedItems = state.cart.items.map((item, index) =>
          index === existingItemIndex
            ? { ...item, quantity: item.quantity + action.payload.quantity }
            : item
        );
      } else {
        updatedItems = [...state.cart.items, action.payload];
      }

      const totalItems = updatedItems.reduce((total, item) => total + item.quantity, 0);
      const totalPrice = updatedItems.reduce(
        (total, item) => total + (item.product.price * item.quantity), 0
      );

      return {
        ...state,
        cart: {
          items: updatedItems,
          totalItems,
          totalPrice
        }
      };

    case actionTypes.UPDATE_CART_ITEM:
      const updatedCartItems = state.cart.items.map(item =>
        item.product._id === action.payload.productId
          ? { ...item, quantity: action.payload.quantity }
          : item
      );

      return {
        ...state,
        cart: {
          ...state.cart,
          items: updatedCartItems,
          totalItems: updatedCartItems.reduce((total, item) => total + item.quantity, 0),
          totalPrice: updatedCartItems.reduce(
            (total, item) => total + (item.product.price * item.quantity), 0
          )
        }
      };

    case actionTypes.REMOVE_FROM_CART:
      const filteredItems = state.cart.items.filter(
        item => item.product._id !== action.payload
      );

      return {
        ...state,
        cart: {
          items: filteredItems,
          totalItems: filteredItems.reduce((total, item) => total + item.quantity, 0),
          totalPrice: filteredItems.reduce(
            (total, item) => total + (item.product.price * item.quantity), 0
          )
        }
      };

    case actionTypes.CLEAR_CART:
      return {
        ...state,
        cart: initialState.cart
      };

    case actionTypes.SET_WISHLIST:
      return {
        ...state,
        wishlist: action.payload
      };

    case actionTypes.ADD_TO_WISHLIST:
      return {
        ...state,
        wishlist: [...state.wishlist, action.payload]
      };

    case actionTypes.REMOVE_FROM_WISHLIST:
      return {
        ...state,
        wishlist: state.wishlist.filter(item => item._id !== action.payload)
      };

    case actionTypes.SET_THEME:
      return {
        ...state,
        theme: action.payload
      };

    default:
      return state;
  }
};

// Create context
const AppContext = createContext();

// Context provider component
export const AppProvider = ({ children }) => {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // Initialize app
  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      const token = localStorage.getItem('token');
      if (token) {
        const userData = await authAPI.getMe();
        dispatch({ type: actionTypes.SET_USER, payload: userData.user });
        
        // Load cart if user is authenticated
        await loadCart();
      } else {
        dispatch({ type: actionTypes.SET_LOADING, payload: false });
      }
    } catch (error) {
      console.error('App initialization error:', error);
      localStorage.removeItem('token');
      dispatch({ type: actionTypes.SET_LOADING, payload: false });
    }
  };

  const login = async (credentials) => {
    try {
      const response = await authAPI.login(credentials);
      localStorage.setItem('token', response.token);
      dispatch({ type: actionTypes.SET_USER, payload: response.user });
      await loadCart();
      toast.success('Login successful!');
      return response;
    } catch (error) {
      toast.error(error.message);
      throw error;
    }
  };

  const register = async (userData) => {
    try {
      const response = await authAPI.register(userData);
      localStorage.setItem('token', response.token);
      dispatch({ type: actionTypes.SET_USER, payload: response.user });
      toast.success('Registration successful!');
      return response;
    } catch (error) {
      console.error('Registration error details:', error);
      const errorMessage = error.data?.errors ?
        error.data.errors.map(e => e.message).join(', ') :
        error.message || 'Registration failed';
      toast.error(errorMessage);
      throw error;
    }
  };

  const logout = () => {
    localStorage.removeItem('token');
    dispatch({ type: actionTypes.LOGOUT });
    toast.success('Logged out successfully');
  };

  const loadCart = async () => {
    try {
      const cartData = await cartAPI.getCart();
      dispatch({ type: actionTypes.SET_CART, payload: cartData.data });
    } catch (error) {
      console.error('Load cart error:', error);
    }
  };

  const addToCart = async (product, quantity = 1) => {
    try {
      if (state.isAuthenticated) {
        await cartAPI.addToCart(product._id, quantity);
        await loadCart();
      } else {
        dispatch({ 
          type: actionTypes.ADD_TO_CART, 
          payload: { product, quantity } 
        });
      }
      toast.success('Added to cart!');
    } catch (error) {
      toast.error(error.message);
    }
  };

  const updateCartItem = async (productId, quantity) => {
    try {
      if (state.isAuthenticated) {
        await cartAPI.updateCartItem(productId, quantity);
        await loadCart();
      } else {
        dispatch({ 
          type: actionTypes.UPDATE_CART_ITEM, 
          payload: { productId, quantity } 
        });
      }
    } catch (error) {
      toast.error(error.message);
    }
  };

  const removeFromCart = async (productId) => {
    try {
      if (state.isAuthenticated) {
        await cartAPI.removeFromCart(productId);
        await loadCart();
      } else {
        dispatch({ type: actionTypes.REMOVE_FROM_CART, payload: productId });
      }
      toast.success('Removed from cart');
    } catch (error) {
      toast.error(error.message);
    }
  };

  const clearCart = async () => {
    try {
      if (state.isAuthenticated) {
        await cartAPI.clearCart();
      }
      dispatch({ type: actionTypes.CLEAR_CART });
      toast.success('Cart cleared');
    } catch (error) {
      toast.error(error.message);
    }
  };

  const addToWishlist = async (product) => {
    try {
      if (!state.isAuthenticated) {
        toast.error('Please login to add to wishlist');
        return;
      }
      
      // API call would go here
      dispatch({ type: actionTypes.ADD_TO_WISHLIST, payload: product });
      toast.success('Added to wishlist!');
    } catch (error) {
      toast.error(error.message);
    }
  };

  const removeFromWishlist = async (productId) => {
    try {
      // API call would go here
      dispatch({ type: actionTypes.REMOVE_FROM_WISHLIST, payload: productId });
      toast.success('Removed from wishlist');
    } catch (error) {
      toast.error(error.message);
    }
  };

  const value = {
    ...state,
    login,
    register,
    logout,
    addToCart,
    updateCartItem,
    removeFromCart,
    clearCart,
    addToWishlist,
    removeFromWishlist,
    loadCart
  };

  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  );
};

// Custom hook to use the context
export const useApp = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};

export default AppContext;
